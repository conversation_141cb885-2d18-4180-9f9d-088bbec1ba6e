from datetime import datetime
from typing import Optional, List, Dict, Any, Union
from uuid import UUID
from pydantic import BaseModel, Field

from app.models.proceso_cliente import UnidadRepeticion, PersonaResponsable


# Base Models
class TareaClienteBase(BaseModel):
    """Base model for tarea cliente data."""
    nombre_tarea_cliente: str = Field(..., description="Nombre de la tarea del cliente")
    descripcion_tarea_cliente: Optional[str] = Field(None, description="Descripción de la tarea")
    duracion_minutos_estimada: Optional[int] = Field(None, description="Duración estimada en minutos")
    unidad_repeticion_base: Optional[str] = Field(None, description="Unidad de repetición")
    numero_ocurrencias_frecuencia: Optional[int] = Field(1, description="Número de ocurrencias por unidad")
    es_manual_cliente: Optional[bool] = Field(True, description="Si la tarea es manual")
    herramientas_utilizadas_cliente: Optional[Union[Dict[str, Any], List[Any]]] = Field(None, description="Herramientas utilizadas")
    puntos_dolor_cliente: Optional[str] = Field(None, description="Puntos de dolor identificados")
    oportunidades_mejora_cliente: Optional[str] = Field(None, description="Oportunidades de mejora")
    info_adicional: Optional[str] = Field(None, description="Información adicional")


class TareaClienteCreate(TareaClienteBase):
    """Model for creating a new tarea cliente."""
    proceso_cliente_id: UUID = Field(..., description="ID del proceso cliente")


class TareaClienteUpdate(BaseModel):
    """Model for updating tarea cliente data."""
    nombre_tarea_cliente: Optional[str] = None
    descripcion_tarea_cliente: Optional[str] = None
    duracion_minutos_estimada: Optional[int] = None
    unidad_repeticion_base: Optional[str] = None
    numero_ocurrencias_frecuencia: Optional[int] = None
    es_manual_cliente: Optional[bool] = None
    herramientas_utilizadas_cliente: Optional[Dict[str, Any]] = None
    puntos_dolor_cliente: Optional[str] = None
    oportunidades_mejora_cliente: Optional[str] = None
    info_adicional: Optional[str] = None


class TareaClienteInDBBase(TareaClienteBase):
    """Base model for tarea cliente data stored in the database."""
    id: UUID
    proceso_cliente_id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Response Models
class TareaClienteListItem(TareaClienteInDBBase):
    """Model for tarea cliente in list views."""
    # Calculated fields
    tiempo_estimado_horas_mes: Optional[float] = Field(0.0, description="Tiempo estimado normalizado a horas/mes")
    
    # Related data
    responsables: Optional[List[PersonaResponsable]] = Field(default_factory=list)


class TareaClienteDetalle(TareaClienteInDBBase):
    """Model for detailed tarea cliente view."""
    # Calculated fields
    tiempo_estimado_horas_mes: Optional[float] = Field(0.0, description="Tiempo estimado normalizado a horas/mes")
    
    # Related data
    responsables: Optional[List[PersonaResponsable]] = Field(default_factory=list)


# Filter Models
class TareaClienteFilters(BaseModel):
    """Model for tarea cliente filters."""
    responsable_ids: Optional[List[UUID]] = Field(None, description="IDs de responsables")
    es_manual_cliente: Optional[bool] = None
    search: Optional[str] = Field(None, description="Búsqueda en nombre y descripción")


# List Response Models
class TareaClienteListResponse(BaseModel):
    """Response model for tarea cliente list endpoints."""
    tareas: List[TareaClienteListItem]
    total: int
    tiempo_total_horas_mes: float = Field(0.0, description="Tiempo total estimado de todas las tareas")


# Summary Models for Dashboard
class ProcesoClienteContadores(BaseModel):
    """Model for proceso cliente counters."""
    total_procesos: int = 0
    total_tareas: int = 0
    tiempo_total_horas_mes: float = 0.0
