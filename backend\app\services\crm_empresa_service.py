from uuid import uuid4, UUID # Added UUID
import datetime

from app.models.crm_empresa import (
    EmpresaCreate,
    Empresa,
    EmpresaGeneralDetails,
    DepartmentInfo,
    ProjectInfo,
    MeetingStats,
    ProcessStats,
    FindingDistribution,
    PeoplePanel,
    EmpresaHallazgosDetails,
    HallazgoListItem,
    HallazgoDetail,
    HallazgoTypeStats,
    HallazgoFilters,
    PersonaDisponible,
    DepartamentoDisponible,
    # Reuniones Tab Models
    EmpresaReunionesDetails,
    PersonaEstadoEntrevista,
    ReunionListItem,
    ReunionTimelineItem,
    ReunionStats
)
# Assuming your SQLAlchemy model is defined in a similar path, e.g., app.db_models.empresa
# For now, let's assume a placeholder or direct interaction if Supabase client is used differently.
# from app.db_models.empresa import Empresa as DBEmpresa # Placeholder for SQLAlchemy model
from app.core.database import get_supabase_client # Or your DB session manager

async def create_empresa(empresa_in: EmpresaCreate, user_id: UUID) -> dict: # Returning dict for Supabase, removed db: Session
    """
    Create a new empresa.
    """
    supabase = await get_supabase_client() # Added await
    
    empresa_data = empresa_in.dict(exclude_none=True)
    empresa_data['id'] = str(uuid4())
    empresa_data['fecha_alta'] = datetime.date.today().isoformat()
    # user_id is not directly on 'empresas' table according to schema,
    # but might be relevant for audit or if schema changes.
    # For now, it's not added to empresa_data unless schema supports 'creado_por_usuario_id' or similar.

    # Ensure 'tipo_relacion' is one of the allowed ENUM values if your DB enforces it strictly
    # Pydantic model already validates this with Literal.

    try:
        # Using Supabase client directly
        response = await supabase.table('empresas').insert(empresa_data).execute()
        
        if response.data and len(response.data) > 0:
            return response.data[0]
        else:
            # Handle case where insert succeeded but no data returned (should not happen with single insert)
            # Or if there was an error not caught by an exception (e.g. RLS preventing read back)
            # For now, we assume an error if data is empty.
            # Consider logging the full response for debugging.
            print(f"Supabase insert response: {response}") # Basic logging
            raise Exception("Failed to create empresa or retrieve created data.")

    except Exception as e:
        # Log the error e
        print(f"Error creating empresa: {e}") # Basic logging
        # Consider re-raising a custom service exception
        raise e

# Placeholder for get_empresa, get_empresas, update_empresa, delete_empresa if needed later

async def get_empresas(search: str | None = None, skip: int = 0, limit: int = 100) -> list[dict]:
    """
    Retrieve empresas, with optional search.
    """
    supabase = await get_supabase_client() # Added await
    query = supabase.table('empresas').select("*").order('nombre').offset(skip).limit(limit)

    if search:
        # Using or filter for searching in 'nombre' or 'nif_cif'
        # The specific Supabase syntax for ilike on multiple fields might need adjustment
        # This is a basic example; more complex search might require raw SQL or different client methods
        # query = query.or_(f"nombre.ilike.%{search}%,nif_cif.ilike.%{search}%") # This syntax might be incorrect for Supabase-py
        
        # Corrected approach for Supabase: build OR condition string
        # This assumes 'nombre' and 'nif_cif' are text fields.
        # Adjust if your schema or search needs are different.
        search_term = f"%{search}%"
        query = query.or_(f"nombre.ilike.{search_term},nif_cif.ilike.{search_term}")


    try:
        response = query.execute() # Removed await
        if response.data:
            # Validate data against the Empresa model before returning
            # Assuming Empresa model is imported from app.models.crm_empresa
            from app.models.crm_empresa import Empresa # Add import
            from pydantic import ValidationError # Import ValidationError

            validated_empresas = []
            for empresa_data in response.data:
                try:
                    validated_empresas.append(Empresa.model_validate(empresa_data))
                except ValidationError as ve:
                    print(f"Pydantic ValidationError for empresa_data: {empresa_data}")
                    print(f"Details: {ve.errors()}")
                    # Decide if you want to skip this record or raise immediately
                    # For now, let's raise to see the first error clearly
                    raise ve 
            return validated_empresas
        return []
    except ValidationError as ve_outer: # Catch ValidationError specifically
        # This will catch the re-raised ValidationError from the loop
        print(f"Outer Pydantic ValidationError in get_empresas: {ve_outer.errors()}")
        raise ve_outer
    except Exception as e: 
        print(f"Generic error fetching or validating empresas: {e}")
        raise e

async def get_empresa_by_id(empresa_id: UUID) -> dict | None:
    """
    Retrieve a specific empresa by ID.
    """
    supabase = await get_supabase_client()

    try:
        response = supabase.table('empresas').select("*").eq('id', str(empresa_id)).execute()

        if response.data and len(response.data) > 0:
            # Validate data against the Empresa model before returning
            from app.models.crm_empresa import Empresa
            from pydantic import ValidationError

            empresa_data = response.data[0]
            try:
                validated_empresa = Empresa.model_validate(empresa_data)
                return validated_empresa.model_dump()
            except ValidationError as ve:
                print(f"Pydantic ValidationError for empresa_data: {empresa_data}")
                print(f"Details: {ve.errors()}")
                raise ve

        return None  # Empresa not found

    except ValidationError as ve:
        print(f"Pydantic ValidationError in get_empresa_by_id: {ve.errors()}")
        raise ve
    except Exception as e:
        print(f"Error fetching empresa by ID {empresa_id}: {e}")
        raise e

async def get_empresa_general_details(empresa_id: UUID) -> EmpresaGeneralDetails:
    """
    Get comprehensive general details for a specific empresa including:
    - Basic empresa info
    - Departments
    - Related projects with progress
    - Active workers count
    - Meeting/interview statistics
    - Client processes and tasks statistics
    - Findings distribution
    - People panel data
    """
    supabase = await get_supabase_client()

    try:
        # 1. Get basic empresa info
        empresa_response = supabase.table('empresas').select("*").eq('id', str(empresa_id)).execute()
        if not empresa_response.data or len(empresa_response.data) == 0:
            raise Exception(f"Empresa with ID {empresa_id} not found")

        empresa_data = empresa_response.data[0]
        empresa = Empresa.model_validate(empresa_data)

        # 2. Get departments
        dept_response = supabase.table('departamentos').select("id, nombre, descripcion").eq('empresa_id', str(empresa_id)).execute()
        departamentos = [DepartmentInfo.model_validate(dept) for dept in (dept_response.data or [])]

        # 3. Get related projects with progress
        projects_response = supabase.table('proyectos_empresas').select(
            "proyecto_id, proyectos!inner(id, nombre, descripcion, progreso, estado, fecha_inicio, fecha_fin_estimada)"
        ).eq('empresa_id', str(empresa_id)).execute()

        proyectos_relacionados = []
        for proj_rel in (projects_response.data or []):
            if proj_rel.get('proyectos'):
                proj_data = proj_rel['proyectos']
                proyectos_relacionados.append(ProjectInfo.model_validate(proj_data))

        # 4. Get active workers count
        workers_response = supabase.table('personas').select("id", count="exact").eq('empresa_id', str(empresa_id)).eq('activo', True).execute()
        total_trabajadores_activos = workers_response.count or 0

        # 5. Get meeting statistics
        meetings_response = supabase.table('reunion_empresas_asociadas').select(
            "reunion_id, reuniones!inner(id, entrevista)"
        ).eq('empresa_id', str(empresa_id)).execute()

        total_reuniones = len(meetings_response.data or [])
        total_entrevistas = sum(1 for meeting in (meetings_response.data or [])
                               if meeting.get('reuniones', {}).get('entrevista', False))

        reuniones_vs_entrevistas_ratio = (total_entrevistas / total_reuniones * 100) if total_reuniones > 0 else 0.0

        estadisticas_reuniones = MeetingStats(
            total_reuniones=total_reuniones,
            total_entrevistas=total_entrevistas,
            reuniones_vs_entrevistas_ratio=reuniones_vs_entrevistas_ratio
        )

        # 6. Get client processes and tasks statistics
        processes_response = supabase.table('procesos_clientes').select("id", count="exact").eq('empresa_cliente_id', str(empresa_id)).execute()
        total_procesos_clientes = processes_response.count or 0

        # Get tasks count through processes
        if total_procesos_clientes > 0:
            process_ids_response = supabase.table('procesos_clientes').select("id").eq('empresa_cliente_id', str(empresa_id)).execute()
            process_ids = [proc['id'] for proc in (process_ids_response.data or [])]

            if process_ids:
                tasks_response = supabase.table('tareas_clientes').select("id", count="exact").in_('proceso_cliente_id', process_ids).execute()
                total_tareas_clientes = tasks_response.count or 0
            else:
                total_tareas_clientes = 0
        else:
            total_tareas_clientes = 0

        estadisticas_procesos = ProcessStats(
            total_procesos_clientes=total_procesos_clientes,
            total_tareas_clientes=total_tareas_clientes
        )

        # 7. Get findings distribution
        findings_response = supabase.table('hallazgos_reuniones').select("tipo").eq('empresa_id', str(empresa_id)).execute()

        # Count findings by type
        findings_count = {}
        total_findings = len(findings_response.data or [])

        for finding in (findings_response.data or []):
            tipo = finding.get('tipo', 'Sin clasificar')
            findings_count[tipo] = findings_count.get(tipo, 0) + 1

        distribucion_hallazgos = []
        for tipo, count in findings_count.items():
            percentage = (count / total_findings * 100) if total_findings > 0 else 0.0
            distribucion_hallazgos.append(FindingDistribution(
                tipo=tipo,
                count=count,
                percentage=percentage
            ))

        # 8. Get people panel data
        people_response = supabase.table('personas').select("id, entrevistado").eq('empresa_id', str(empresa_id)).eq('activo', True).execute()

        total_personas_activas = len(people_response.data or [])
        total_personas_entrevistadas = sum(1 for person in (people_response.data or [])
                                         if person.get('entrevistado', False))

        porcentaje_entrevistadas = (total_personas_entrevistadas / total_personas_activas * 100) if total_personas_activas > 0 else 0.0

        panel_personas = PeoplePanel(
            total_personas_activas=total_personas_activas,
            total_personas_entrevistadas=total_personas_entrevistadas,
            porcentaje_entrevistadas=porcentaje_entrevistadas
        )

        # 9. Build final response
        return EmpresaGeneralDetails(
            empresa=empresa,
            departamentos=departamentos,
            proyectos_relacionados=proyectos_relacionados,
            total_trabajadores_activos=total_trabajadores_activos,
            estadisticas_reuniones=estadisticas_reuniones,
            estadisticas_procesos=estadisticas_procesos,
            distribucion_hallazgos=distribucion_hallazgos,
            panel_personas=panel_personas
        )

    except Exception as e:
        print(f"Error fetching empresa general details for ID {empresa_id}: {e}")
        raise e

async def get_empresa_hallazgos_details(empresa_id: UUID) -> EmpresaHallazgosDetails:
    """
    Get comprehensive hallazgos details for a specific empresa including:
    - List of all findings with basic info
    - Statistics and distribution by type
    - Available filter options
    """
    supabase = await get_supabase_client()

    try:
        # 1. Get all hallazgos for this empresa with joined data
        hallazgos_response = supabase.table('hallazgos_reuniones').select(
            """
            id, titulo, tipo, impacto, descripcion, posible_solucion, estado,
            procesos_relacionados, created_at, updated_at,
            departamentos!inner(id, nombre),
            personas!inner(id, nombre, apellidos)
            """
        ).eq('empresa_id', str(empresa_id)).order('created_at', desc=True).execute()

        hallazgos_raw = hallazgos_response.data or []

        # 2. Process hallazgos data and resolve related processes
        hallazgos_list = []
        for hallazgo in hallazgos_raw:
            # Get department and person names
            dept_name = hallazgo.get('departamentos', {}).get('nombre') if hallazgo.get('departamentos') else None
            persona_data = hallazgo.get('personas', {}) if hallazgo.get('personas') else {}
            persona_nombre = None
            if persona_data and persona_data.get('nombre'):
                apellidos = persona_data.get('apellidos', '')
                persona_nombre = f"{persona_data['nombre']} {apellidos}".strip()

            hallazgo_item = HallazgoListItem(
                id=hallazgo['id'],
                titulo=hallazgo.get('titulo') or 'Sin título',
                tipo=hallazgo.get('tipo') or 'Sin clasificar',
                impacto=hallazgo.get('impacto') or 'Sin especificar',
                departamento_nombre=dept_name,
                persona_nombre=persona_nombre,
                estado=hallazgo.get('estado'),
                created_at=hallazgo['created_at']
            )
            hallazgos_list.append(hallazgo_item)

        # 3. Calculate type distribution statistics
        tipo_counts = {}
        total_hallazgos = len(hallazgos_list)

        for hallazgo in hallazgos_list:
            tipo = hallazgo.tipo or 'Sin clasificar'
            tipo_counts[tipo] = tipo_counts.get(tipo, 0) + 1

        distribucion_tipos = []
        for tipo, count in tipo_counts.items():
            percentage = (count / total_hallazgos * 100) if total_hallazgos > 0 else 0.0
            distribucion_tipos.append(HallazgoTypeStats(
                tipo=tipo,
                count=count,
                percentage=percentage
            ))

        # Sort by count descending
        distribucion_tipos.sort(key=lambda x: x.count, reverse=True)

        # 4. Get filter options
        # Available personas for this empresa
        personas_response = supabase.table('personas').select(
            "id, nombre, apellidos"
        ).eq('empresa_id', str(empresa_id)).eq('activo', True).order('nombre').execute()

        personas_disponibles = []
        for persona in (personas_response.data or []):
            nombre_completo = f"{persona['nombre']} {persona.get('apellidos', '')}".strip()
            personas_disponibles.append(PersonaDisponible(
                id=persona['id'],
                nombre_completo=nombre_completo
            ))

        # Available departamentos for this empresa
        dept_response = supabase.table('departamentos').select(
            "id, nombre"
        ).eq('empresa_id', str(empresa_id)).order('nombre').execute()

        departamentos_disponibles = [
            DepartamentoDisponible(id=dept['id'], nombre=dept['nombre'])
            for dept in (dept_response.data or [])
        ]

        # Available tipos (from ENUM)
        tipos_disponibles = [
            'ineficiencia',
            'ladron_tiempo',
            'oportunidad_mejora',
            'riesgo_identificado',
            'deficit_gobernanza_datos',
            'falta_estandarizacion',
            'equipamiento_inadecuado'
        ]

        # Available impactos (distinct values from this empresa's hallazgos)
        impactos_disponibles = list(set(
            hallazgo.impacto for hallazgo in hallazgos_list
            if hallazgo.impacto and hallazgo.impacto.strip() and hallazgo.impacto != 'Sin especificar'
        ))
        impactos_disponibles.sort()

        # 5. Build filter options
        filtros_disponibles = HallazgoFilters(
            personas_disponibles=personas_disponibles,
            departamentos_disponibles=departamentos_disponibles,
            tipos_disponibles=tipos_disponibles,
            impactos_disponibles=impactos_disponibles
        )

        # 6. Build final response
        return EmpresaHallazgosDetails(
            total_hallazgos=total_hallazgos,
            distribucion_tipos=distribucion_tipos,
            hallazgos=hallazgos_list,
            filtros_disponibles=filtros_disponibles
        )

    except Exception as e:
        print(f"Error fetching empresa hallazgos details for ID {empresa_id}: {e}")
        raise e

async def get_hallazgo_detail(hallazgo_id: UUID) -> HallazgoDetail:
    """
    Get detailed information for a specific hallazgo including related processes.
    """
    supabase = await get_supabase_client()

    try:
        # Get hallazgo with joined data
        hallazgo_response = supabase.table('hallazgos_reuniones').select(
            """
            id, titulo, tipo, impacto, descripcion, posible_solucion, estado,
            procesos_relacionados, created_at, updated_at,
            departamentos(id, nombre),
            personas(id, nombre, apellidos)
            """
        ).eq('id', str(hallazgo_id)).execute()

        if not hallazgo_response.data:
            raise Exception(f"Hallazgo with ID {hallazgo_id} not found")

        hallazgo = hallazgo_response.data[0]

        # Get department and person names
        dept_name = hallazgo.get('departamentos', {}).get('nombre') if hallazgo.get('departamentos') else None
        persona_data = hallazgo.get('personas', {}) if hallazgo.get('personas') else {}
        persona_nombre = None
        if persona_data and persona_data.get('nombre'):
            apellidos = persona_data.get('apellidos', '')
            persona_nombre = f"{persona_data['nombre']} {apellidos}".strip()

        # Parse and resolve related processes
        procesos_relacionados = []
        if hallazgo.get('procesos_relacionados'):
            try:
                import json
                proceso_ids = hallazgo['procesos_relacionados']
                if isinstance(proceso_ids, str):
                    proceso_ids = json.loads(proceso_ids)

                if isinstance(proceso_ids, list) and proceso_ids:
                    # Get process names from procesos_clientes table
                    procesos_response = supabase.table('procesos_clientes').select(
                        "id, nombre"
                    ).in_('id', proceso_ids).execute()

                    procesos_relacionados = [
                        proceso['nombre'] for proceso in (procesos_response.data or [])
                        if proceso.get('nombre')
                    ]
            except (json.JSONDecodeError, TypeError) as e:
                print(f"Error parsing procesos_relacionados for hallazgo {hallazgo_id}: {e}")
                procesos_relacionados = []

        return HallazgoDetail(
            id=hallazgo['id'],
            titulo=hallazgo.get('titulo') or 'Sin título',
            tipo=hallazgo.get('tipo') or 'Sin clasificar',
            impacto=hallazgo.get('impacto') or 'Sin especificar',
            descripcion=hallazgo.get('descripcion'),
            posible_solucion=hallazgo.get('posible_solucion'),
            estado=hallazgo.get('estado'),
            departamento_nombre=dept_name,
            persona_nombre=persona_nombre,
            procesos_relacionados=procesos_relacionados,
            created_at=hallazgo['created_at'],
            updated_at=hallazgo['updated_at']
        )

    except Exception as e:
        print(f"Error fetching hallazgo detail for ID {hallazgo_id}: {e}")
        raise e

async def get_empresa_reuniones_details(empresa_id: UUID) -> EmpresaReunionesDetails:
    """
    Get comprehensive reuniones details for a specific empresa including:
    - List of meetings/interviews associated with the company
    - Company people with interview status
    - Timeline of meetings
    - Statistics
    """
    supabase = await get_supabase_client()

    try:
        # 1. Get all reuniones associated with this empresa
        reuniones_response = supabase.table('reunion_empresas_asociadas').select(
            """
            reuniones(
                id, titulo, fecha_reunion, entrevista, estado_procesamiento,
                transcripcion_final, resumen, created_at, updated_at
            )
            """
        ).eq('empresa_id', str(empresa_id)).execute()

        reuniones_data = []
        if reuniones_response.data:
            reuniones_data = [item['reuniones'] for item in reuniones_response.data if item.get('reuniones')]

        # 2. Get all personas from this empresa
        personas_response = supabase.table('personas').select(
            "id, nombre, apellidos, cargo, entrevistado, activo"
        ).eq('empresa_id', str(empresa_id)).eq('activo', True).execute()

        personas_data = personas_response.data or []

        # 3. For each reunion, get the personas asistentes from this company
        reuniones_list = []
        timeline_items = []

        for reunion in reuniones_data:
            if not reunion:
                continue

            reunion_id = reunion['id']

            # Get personas asistentes for this reunion from this company
            asistentes_response = supabase.table('reunion_personas_asociadas').select(
                """
                personas(id, nombre, apellidos, empresa_id)
                """
            ).eq('reunion_id', reunion_id).execute()

            # Filter only personas from this empresa
            personas_asistentes = []
            personas_asistentes_ids = []
            if asistentes_response.data:
                for asistente in asistentes_response.data:
                    persona = asistente.get('personas')
                    if persona and persona.get('empresa_id') == str(empresa_id):
                        nombre_completo = f"{persona['nombre']} {persona.get('apellidos', '')}".strip()
                        personas_asistentes.append(nombre_completo)
                        personas_asistentes_ids.append(persona['id'])

            # Determine tipo based on entrevista boolean
            tipo = "Entrevista" if reunion.get('entrevista') else "Reunión"

            # Create reunion list item
            reunion_item = ReunionListItem(
                id=reunion['id'],
                titulo=reunion.get('titulo'),
                fecha_reunion=reunion.get('fecha_reunion'),
                tipo=tipo,
                estado_procesamiento=reunion.get('estado_procesamiento'),
                personas_asistentes=personas_asistentes,
                personas_asistentes_ids=personas_asistentes_ids
            )
            reuniones_list.append(reunion_item)

            # Create timeline item
            timeline_item = ReunionTimelineItem(
                id=reunion['id'],
                titulo=reunion.get('titulo'),
                fecha_reunion=reunion.get('fecha_reunion'),
                tipo=tipo,
                resumen_breve=reunion.get('resumen'),
                personas_asistentes=personas_asistentes
            )
            timeline_items.append(timeline_item)

        # 4. Calculate statistics for each persona (how many meetings they attended)
        personas_empresa = []
        for persona in personas_data:
            persona_id = persona['id']

            # Count meetings this persona attended
            total_reuniones = sum(1 for reunion in reuniones_list if persona_id in reunion.personas_asistentes_ids)

            persona_estado = PersonaEstadoEntrevista(
                id=persona['id'],
                nombre=persona['nombre'],
                apellidos=persona.get('apellidos'),
                cargo=persona.get('cargo'),
                entrevistado=persona.get('entrevistado', False),
                total_reuniones_asistidas=total_reuniones
            )
            personas_empresa.append(persona_estado)

        # 5. Calculate overall statistics
        total_reuniones = len([r for r in reuniones_list if r.tipo == "Reunión"])
        total_entrevistas = len([r for r in reuniones_list if r.tipo == "Entrevista"])
        total_personas_entrevistadas = len([p for p in personas_empresa if p.entrevistado])
        total_personas = len(personas_empresa)
        porcentaje_entrevistadas = (total_personas_entrevistadas / total_personas * 100) if total_personas > 0 else 0.0

        estadisticas = ReunionStats(
            total_reuniones=total_reuniones,
            total_entrevistas=total_entrevistas,
            total_personas_entrevistadas=total_personas_entrevistadas,
            porcentaje_personas_entrevistadas=round(porcentaje_entrevistadas, 1)
        )

        # 6. Sort timeline items chronologically (newest first)
        timeline_items.sort(key=lambda x: x.fecha_reunion or datetime.datetime.min, reverse=True)

        # 7. Sort reuniones list chronologically (newest first)
        reuniones_list.sort(key=lambda x: x.fecha_reunion or datetime.datetime.min, reverse=True)

        return EmpresaReunionesDetails(
            reuniones_list=reuniones_list,
            personas_empresa=personas_empresa,
            timeline_items=timeline_items,
            estadisticas=estadisticas,
            total_reuniones=len(reuniones_list)
        )

    except Exception as e:
        print(f"Error fetching empresa reuniones details for empresa_id {empresa_id}: {e}")
        raise e
