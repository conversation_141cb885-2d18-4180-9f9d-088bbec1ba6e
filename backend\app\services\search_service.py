from fastapi import HTTPException, status
from uuid import UUID
import datetime
from typing import List, Dict, Any, Optional

from app.core.database import get_supabase_client

async def global_search(
    query: str,
    limit: int = 50,
    include_proyectos: bool = True,
    include_procesos: bool = True,
    include_tareas: bool = True
) -> Dict[str, Any]:
    """
    Perform global search across proyectos, procesos, and tareas.
    """
    supabase = await get_supabase_client()
    
    try:
        results = {
            "query": query,
            "proyectos": [],
            "procesos": [],
            "tareas": [],
            "total_results": 0
        }
        
        search_pattern = f"%{query}%"
        
        # Search in proyectos
        if include_proyectos:
            proyectos_result = supabase.table('proyectos').select(
                """
                id, nombre, descripcion, estado, prioridad, progreso,
                responsable_usuario_id, fecha_fin_estimada,
                usuarios:responsable_usuario_id(nombre)
                """
            ).or_(
                f'nombre.ilike.{search_pattern},descripcion.ilike.{search_pattern},objetivo.ilike.{search_pattern}'
            ).limit(limit // 3 if limit > 3 else limit).execute()
            
            for proyecto in proyectos_result.data or []:
                results["proyectos"].append({
                    "id": proyecto["id"],
                    "type": "proyecto",
                    "title": proyecto["nombre"],
                    "description": proyecto.get("descripcion", ""),
                    "estado": proyecto.get("estado"),
                    "prioridad": proyecto.get("prioridad"),
                    "progreso": proyecto.get("progreso", 0),
                    "responsable": proyecto.get("usuarios", {}).get("nombre") if proyecto.get("usuarios") else None,
                    "fecha_fin_estimada": proyecto.get("fecha_fin_estimada"),
                    "url": f"/proyectos/{proyecto['id']}"
                })
        
        # Search in procesos
        if include_procesos:
            procesos_result = supabase.table('procesos').select(
                """
                id, nombre, descripcion, tipo_proceso, estado, es_cuello_botella,
                empresa_id, tiempo_estimado_manual,
                empresas:empresa_id(nombre),
                usuarios:persona_id(nombre)
                """
            ).or_(
                f'nombre.ilike.{search_pattern},descripcion.ilike.{search_pattern}'
            ).limit(limit // 3 if limit > 3 else limit).execute()
            
            for proceso in procesos_result.data or []:
                results["procesos"].append({
                    "id": proceso["id"],
                    "type": "proceso",
                    "title": proceso["nombre"],
                    "description": proceso.get("descripcion", ""),
                    "tipo_proceso": proceso.get("tipo_proceso"),
                    "estado": proceso.get("estado"),
                    "es_cuello_botella": proceso.get("es_cuello_botella", False),
                    "empresa": proceso.get("empresas", {}).get("nombre") if proceso.get("empresas") else None,
                    "responsable": proceso.get("usuarios", {}).get("nombre") if proceso.get("usuarios") else None,
                    "tiempo_estimado": proceso.get("tiempo_estimado_manual"),
                    "url": f"/procesos/{proceso['id']}"
                })
        
        # Search in tareas
        if include_tareas:
            tareas_result = supabase.table('tareas').select(
                """
                id, titulo, descripcion, estado, prioridad, urgencia,
                fecha_vencimiento, asignado_a, proyecto_id,
                asignado_usuario:usuarios!asignado_a(nombre),
                proyecto:proyectos!proyecto_id(nombre)
                """
            ).or_(
                f'titulo.ilike.{search_pattern},descripcion.ilike.{search_pattern}'
            ).limit(limit // 3 if limit > 3 else limit).execute()
            
            for tarea in tareas_result.data or []:
                # Calculate if overdue
                es_vencida = False
                if tarea.get('fecha_vencimiento'):
                    fecha_vencimiento = datetime.datetime.fromisoformat(tarea['fecha_vencimiento']).date()
                    es_vencida = fecha_vencimiento < datetime.date.today() and tarea.get('estado') != 'Completada'
                
                results["tareas"].append({
                    "id": tarea["id"],
                    "type": "tarea",
                    "title": tarea["titulo"],
                    "description": tarea.get("descripcion", ""),
                    "estado": tarea.get("estado"),
                    "prioridad": tarea.get("prioridad"),
                    "urgencia": tarea.get("urgencia"),
                    "fecha_vencimiento": tarea.get("fecha_vencimiento"),
                    "es_vencida": es_vencida,
                    "asignado": tarea.get("asignado_usuario", {}).get("nombre") if tarea.get("asignado_usuario") else None,
                    "proyecto": tarea.get("proyecto", {}).get("nombre") if tarea.get("proyecto") else None,
                    "url": f"/tareas/{tarea['id']}"
                })
        
        # Calculate total results
        results["total_results"] = len(results["proyectos"]) + len(results["procesos"]) + len(results["tareas"])
        
        return results
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error performing global search: {str(e)}"
        )

async def search_by_type(
    query: str,
    search_type: str,
    limit: int = 20
) -> List[Dict[str, Any]]:
    """
    Search within a specific type (proyectos, procesos, or tareas).
    """
    supabase = await get_supabase_client()
    
    try:
        search_pattern = f"%{query}%"
        results = []
        
        if search_type == "proyectos":
            proyectos_result = supabase.table('proyectos').select(
                """
                id, nombre, descripcion, estado, prioridad, progreso,
                responsable_usuario_id, fecha_fin_estimada,
                usuarios:responsable_usuario_id(nombre)
                """
            ).or_(
                f'nombre.ilike.{search_pattern},descripcion.ilike.{search_pattern},objetivo.ilike.{search_pattern}'
            ).limit(limit).execute()
            
            for proyecto in proyectos_result.data or []:
                results.append({
                    "id": proyecto["id"],
                    "type": "proyecto",
                    "title": proyecto["nombre"],
                    "description": proyecto.get("descripcion", ""),
                    "estado": proyecto.get("estado"),
                    "prioridad": proyecto.get("prioridad"),
                    "progreso": proyecto.get("progreso", 0),
                    "responsable": proyecto.get("usuarios", {}).get("nombre") if proyecto.get("usuarios") else None,
                    "fecha_fin_estimada": proyecto.get("fecha_fin_estimada"),
                    "url": f"/proyectos/{proyecto['id']}"
                })
        
        elif search_type == "procesos":
            procesos_result = supabase.table('procesos').select(
                """
                id, nombre, descripcion, tipo_proceso, estado, es_cuello_botella,
                empresa_id, tiempo_estimado_manual,
                empresas:empresa_id(nombre),
                usuarios:persona_id(nombre)
                """
            ).or_(
                f'nombre.ilike.{search_pattern},descripcion.ilike.{search_pattern}'
            ).limit(limit).execute()
            
            for proceso in procesos_result.data or []:
                results.append({
                    "id": proceso["id"],
                    "type": "proceso",
                    "title": proceso["nombre"],
                    "description": proceso.get("descripcion", ""),
                    "tipo_proceso": proceso.get("tipo_proceso"),
                    "estado": proceso.get("estado"),
                    "es_cuello_botella": proceso.get("es_cuello_botella", False),
                    "empresa": proceso.get("empresas", {}).get("nombre") if proceso.get("empresas") else None,
                    "responsable": proceso.get("usuarios", {}).get("nombre") if proceso.get("usuarios") else None,
                    "tiempo_estimado": proceso.get("tiempo_estimado_manual"),
                    "url": f"/procesos/{proceso['id']}"
                })
        
        elif search_type == "tareas":
            tareas_result = supabase.table('tareas').select(
                """
                id, titulo, descripcion, estado, prioridad, urgencia,
                fecha_vencimiento, asignado_a, proyecto_id,
                asignado_usuario:usuarios!asignado_a(nombre),
                proyecto:proyectos!proyecto_id(nombre)
                """
            ).or_(
                f'titulo.ilike.{search_pattern},descripcion.ilike.{search_pattern}'
            ).limit(limit).execute()
            
            for tarea in tareas_result.data or []:
                # Calculate if overdue
                es_vencida = False
                if tarea.get('fecha_vencimiento'):
                    fecha_vencimiento = datetime.datetime.fromisoformat(tarea['fecha_vencimiento']).date()
                    es_vencida = fecha_vencimiento < datetime.date.today() and tarea.get('estado') != 'Completada'
                
                results.append({
                    "id": tarea["id"],
                    "type": "tarea",
                    "title": tarea["titulo"],
                    "description": tarea.get("descripcion", ""),
                    "estado": tarea.get("estado"),
                    "prioridad": tarea.get("prioridad"),
                    "urgencia": tarea.get("urgencia"),
                    "fecha_vencimiento": tarea.get("fecha_vencimiento"),
                    "es_vencida": es_vencida,
                    "asignado": tarea.get("asignado_usuario", {}).get("nombre") if tarea.get("asignado_usuario") else None,
                    "proyecto": tarea.get("proyecto", {}).get("nombre") if tarea.get("proyecto") else None,
                    "url": f"/tareas/{tarea['id']}"
                })
        
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid search type: {search_type}. Must be one of: proyectos, procesos, tareas"
            )
        
        return results
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error performing search by type: {str(e)}"
        )

async def get_search_suggestions(query: str, limit: int = 10) -> List[str]:
    """
    Get search suggestions based on partial query.
    """
    supabase = await get_supabase_client()
    
    try:
        suggestions = set()
        search_pattern = f"{query}%"
        
        # Get suggestions from proyectos
        proyectos_result = supabase.table('proyectos').select("nombre").ilike('nombre', search_pattern).limit(limit // 3).execute()
        for proyecto in proyectos_result.data or []:
            suggestions.add(proyecto["nombre"])
        
        # Get suggestions from procesos
        procesos_result = supabase.table('procesos').select("nombre").ilike('nombre', search_pattern).limit(limit // 3).execute()
        for proceso in procesos_result.data or []:
            suggestions.add(proceso["nombre"])
        
        # Get suggestions from tareas
        tareas_result = supabase.table('tareas').select("titulo").ilike('titulo', search_pattern).limit(limit // 3).execute()
        for tarea in tareas_result.data or []:
            suggestions.add(tarea["titulo"])
        
        return sorted(list(suggestions))[:limit]
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting search suggestions: {str(e)}"
        )
