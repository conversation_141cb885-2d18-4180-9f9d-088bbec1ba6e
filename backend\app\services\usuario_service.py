from uuid import UUID
from typing import List, Optional
from app.models.user import User
from app.core.database import get_supabase_client

async def get_usuarios(search: Optional[str] = None, skip: int = 0, limit: int = 100) -> List[User]:
    """
    Retrieve a list of usuarios from the database.
    """
    supabase = await get_supabase_client()
    
    # Build the query
    query = supabase.table('usuarios').select(
        "id, email, nombre, rol, empresa_id, avatar_url, info_adicional, ultimo_acceso, created_at"
    )
    
    # Add search filter if provided
    if search:
        # Search in nombre and email fields
        query = query.or_(f"nombre.ilike.%{search}%,email.ilike.%{search}%")
    
    # Add pagination
    query = query.range(skip, skip + limit - 1)
    
    # Order by nombre
    query = query.order('nombre')
    
    response = query.execute()
    
    if response.data:
        return [User(**usuario) for usuario in response.data]
    return []

async def get_usuario_by_id(usuario_id: UUID) -> Optional[User]:
    """
    Retrieve a specific usuario by ID.
    """
    supabase = await get_supabase_client()
    
    response = supabase.table('usuarios').select(
        "id, email, nombre, rol, empresa_id, avatar_url, info_adicional, ultimo_acceso, created_at"
    ).eq('id', str(usuario_id)).execute()
    
    if response.data and len(response.data) > 0:
        return User(**response.data[0])
    return None
