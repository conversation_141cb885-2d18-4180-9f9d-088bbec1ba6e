import React, { useState, useEffect, useCallback } from 'react';
import { memoryManager } from '../../services/memoryManager';
import { PERFORMANCE_CONFIG } from '../../config/performance';
import { useOptimizedTokenCount } from '../../hooks/useOptimizedTokenCount';
import { ChatMessage } from '../../contexts/ChatContext';

interface PerformanceMonitorProps {
  messages: ChatMessage[];
  isVisible?: boolean;
  onToggle?: () => void;
}

interface PerformanceStats {
  messageCount: number;
  tokenCount: number;
  memoryUsage: number | null;
  cacheSize: number;
  renderTime: number;
  performanceLevel: string;
}

const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  messages,
  isVisible = false,
  onToggle,
}) => {
  const [stats, setStats] = useState<PerformanceStats>({
    messageCount: 0,
    tokenCount: 0,
    memoryUsage: null,
    cacheSize: 0,
    renderTime: 0,
    performanceLevel: 'LOW',
  });

  const { totalTokens, cacheSize } = useOptimizedTokenCount(messages);

  const updateStats = useCallback(() => {
    const memoryStats = memoryManager.getMemoryStats();
    const cacheStats = memoryManager.getCacheStats();
    
    // Calculate performance level
    let performanceLevel = 'LOW';
    if (messages.length > 500 || totalTokens > 50000) {
      performanceLevel = 'HIGH';
    } else if (messages.length > 200 || totalTokens > 10000) {
      performanceLevel = 'MEDIUM';
    }
    if (messages.length > 1000 || totalTokens > 90000) {
      performanceLevel = 'CRITICAL';
    }

    setStats({
      messageCount: messages.length,
      tokenCount: totalTokens,
      memoryUsage: memoryStats.usedJSHeapSize || null,
      cacheSize: Object.values(cacheStats).reduce((total: number, cache: any) => total + cache.size, 0),
      renderTime: 0, // This would be updated by actual render measurements
      performanceLevel,
    });
  }, [messages.length, totalTokens]);

  useEffect(() => {
    updateStats();
    const interval = setInterval(updateStats, 5000); // Update every 5 seconds
    return () => clearInterval(interval);
  }, [updateStats]);

  const formatBytes = (bytes: number | null): string => {
    if (bytes === null) return 'N/A';
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const getPerformanceLevelColor = (level: string): string => {
    switch (level) {
      case 'LOW': return 'text-green-600 bg-green-100';
      case 'MEDIUM': return 'text-yellow-600 bg-yellow-100';
      case 'HIGH': return 'text-orange-600 bg-orange-100';
      case 'CRITICAL': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const handleForceCleanup = () => {
    memoryManager.forceCleanup();
    updateStats();
  };

  if (!isVisible) {
    return (
      <button
        onClick={onToggle}
        className="fixed bottom-4 left-4 bg-gray-800 text-white p-2 rounded-full shadow-lg hover:bg-gray-700 transition-colors z-50"
        title="Show Performance Monitor"
      >
        📊
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 left-4 bg-white border border-gray-300 rounded-lg shadow-lg p-4 max-w-sm z-50">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-semibold text-gray-800">Performance Monitor</h3>
        <button
          onClick={onToggle}
          className="text-gray-500 hover:text-gray-700 text-lg"
          title="Hide Performance Monitor"
        >
          ×
        </button>
      </div>

      <div className="space-y-2 text-xs">
        {/* Performance Level */}
        <div className="flex items-center justify-between">
          <span className="text-gray-600">Level:</span>
          <span className={`px-2 py-1 rounded text-xs font-medium ${getPerformanceLevelColor(stats.performanceLevel)}`}>
            {stats.performanceLevel}
          </span>
        </div>

        {/* Message Count */}
        <div className="flex items-center justify-between">
          <span className="text-gray-600">Messages:</span>
          <span className="font-mono">{stats.messageCount.toLocaleString()}</span>
        </div>

        {/* Token Count */}
        <div className="flex items-center justify-between">
          <span className="text-gray-600">Tokens:</span>
          <span className="font-mono">{stats.tokenCount.toLocaleString()}</span>
        </div>

        {/* Memory Usage */}
        <div className="flex items-center justify-between">
          <span className="text-gray-600">Memory:</span>
          <span className="font-mono">{formatBytes(stats.memoryUsage)}</span>
        </div>

        {/* Cache Size */}
        <div className="flex items-center justify-between">
          <span className="text-gray-600">Cache:</span>
          <span className="font-mono">{stats.cacheSize} items</span>
        </div>

        {/* Token Cache Size */}
        <div className="flex items-center justify-between">
          <span className="text-gray-600">Token Cache:</span>
          <span className="font-mono">{cacheSize} items</span>
        </div>
      </div>

      {/* Actions */}
      <div className="mt-3 pt-3 border-t border-gray-200">
        <button
          onClick={handleForceCleanup}
          className="w-full bg-blue-500 text-white text-xs py-1 px-2 rounded hover:bg-blue-600 transition-colors"
        >
          Force Cleanup
        </button>
      </div>

      {/* Performance Tips */}
      {stats.performanceLevel === 'CRITICAL' && (
        <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs">
          <div className="text-red-800 font-medium mb-1">Performance Critical!</div>
          <div className="text-red-700">
            • Consider starting a new conversation
            <br />
            • Virtual scrolling is active
            <br />
            • Memory cleanup recommended
          </div>
        </div>
      )}

      {stats.performanceLevel === 'HIGH' && (
        <div className="mt-2 p-2 bg-orange-50 border border-orange-200 rounded text-xs">
          <div className="text-orange-800 font-medium mb-1">High Usage</div>
          <div className="text-orange-700">
            • Performance optimizations active
            <br />
            • Consider periodic cleanup
          </div>
        </div>
      )}

      {/* Debug Info (only in development) */}
      {PERFORMANCE_CONFIG.DEV.ENABLE_CONSOLE_LOGS && (
        <div className="mt-2 p-2 bg-gray-50 border border-gray-200 rounded text-xs">
          <div className="text-gray-600 font-medium mb-1">Debug Info</div>
          <div className="text-gray-500 font-mono text-xs">
            Render optimizations: {stats.performanceLevel !== 'LOW' ? 'ON' : 'OFF'}
            <br />
            Virtual scroll: {stats.messageCount > 100 ? 'ON' : 'OFF'}
            <br />
            Lazy loading: {stats.performanceLevel === 'HIGH' || stats.performanceLevel === 'CRITICAL' ? 'ON' : 'OFF'}
          </div>
        </div>
      )}
    </div>
  );
};

export default PerformanceMonitor;
