import React, { useState, useEffect } from 'react';
import { Session } from '@supabase/supabase-js'; // Import Session
import { MeetingState, SpeakerAssignable, AssociatedEntity } from '../../../types/meeting.types';
import Modal from '../../UI/Modal';
import { PersonaApiResponse } from '../../CRM/PersonaCreateForm'; // Import from the form files
import { LeadContactoApiResponse } from '../../CRM/LeadContactoCreateForm';
import PersonaCreateForm from '../../CRM/PersonaCreateForm';
import LeadContactoCreateForm from '../../CRM/LeadContactoCreateForm';
import SelectExistingEntityModal from '../SelectExistingEntityModal';
import { PlayIcon, StopIcon } from '@heroicons/react/24/solid';
import LoadingSpinner from '../../UI/LoadingSpinner'; // Assuming this path is correct
import { useToastContext } from '../../../providers/ToastProvider';

interface SpeakerAssignmentStepProps {
  meetingState: MeetingState;
  onUpdateSpeakerAssignment: (speakerTag: string, assignment: SpeakerAssignable) => void;
  onAddAssociatedEntity: (entity: AssociatedEntity) => void; // For in-context creation
  onUpdateEntrevista: (entrevista: boolean) => Promise<void>; // For updating entrevista field
  onUpdateVideo: (video: boolean) => Promise<void>; // For updating video field
  onSetCurrentStep: (step: number) => void;
  session: Session | null;
  audioPlayerRef: React.RefObject<HTMLAudioElement>;
  navigate: (path: string) => void; // From react-router-dom
}

const SpeakerAssignmentStep: React.FC<SpeakerAssignmentStepProps> = ({
  meetingState,
  onUpdateSpeakerAssignment,
  onAddAssociatedEntity,
  onUpdateEntrevista,
  onUpdateVideo,
  onSetCurrentStep,
  session,
  audioPlayerRef,
  navigate,
}) => {
  // Toast notifications
  const { success, error: showError, warning } = useToastContext();

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeCrmModal, setActiveCrmModal] = useState<'persona' | 'leadContacto' | null>(null);
  const [activeSelectModal, setActiveSelectModal] = useState<'persona' | 'leadContacto' | null>(null);
  const [assigningSpeakerForTag, setAssigningSpeakerForTag] = useState<string | null>(null);

  const [currentPlayingSpeakerId, setCurrentPlayingSpeakerId] = useState<string | null>(null);
  const [isAudioLoading, setIsAudioLoading] = useState<boolean>(false);
  const playbackTimeoutRef = React.useRef<number | null>(null);

  const handleSpeakerAssignmentChange = (speakerTag: string, assignedEntityId: string) => {
    if (assignedEntityId === "") {
      onUpdateSpeakerAssignment(speakerTag, { id: '', tipo: 'persona', nombre: '' }); // Default empty
      return;
    }

    const speaker = meetingState.potentialSpeakers.find(p => p.id === assignedEntityId);
    if (speaker) {
      onUpdateSpeakerAssignment(speakerTag, speaker);
    } else {
      console.warn(`Selected speaker ID ${assignedEntityId} not found in potentialSpeakers list.`);
      onUpdateSpeakerAssignment(speakerTag, { id: '', tipo: 'persona', nombre: '' });
    }
  };

  const openCrmModalForSpeaker = (speakerTag: string, crmType: 'persona' | 'leadContacto') => {
    setAssigningSpeakerForTag(speakerTag);
    setActiveCrmModal(crmType);
  };

  const openSelectModalForSpeaker = (speakerTag: string, entityType: 'persona' | 'leadContacto') => {
    setAssigningSpeakerForTag(speakerTag);
    setActiveSelectModal(entityType);
  };

  const handleExistingEntitySelected = (entities: AssociatedEntity[]) => {
    // Add all selected entities to the meeting
    entities.forEach(entity => {
      onAddAssociatedEntity(entity);

      // If we have a meeting ID, also associate with the reunion in the database
      if (meetingState.id && session?.access_token) {
        fetch(`${import.meta.env.VITE_API_BASE_URL}/reuniones/${meetingState.id}/associations`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${session.access_token}`,
          },
          body: JSON.stringify({
            entity_id: entity.id,
            entity_type: entity.tipo,
          }),
        }).catch(error => {
          console.error('Error associating entity with reunion:', error);
          warning('Advertencia', 'La entidad se seleccionó correctamente pero no se pudo asociar con la reunión.');
        });
      }
    });

    // If we're assigning to a specific speaker, assign the first selected entity
    if (assigningSpeakerForTag && entities.length > 0) {
      const firstEntity = entities[0];
      const speakerEntity: SpeakerAssignable = {
        id: firstEntity.id,
        nombre: firstEntity.nombre,
        tipo: firstEntity.tipo as 'persona' | 'leadContacto',
      };
      onUpdateSpeakerAssignment(assigningSpeakerForTag, speakerEntity);
      setAssigningSpeakerForTag(null);
    }

    setActiveSelectModal(null);
  };

  const handleCrmEntityCreated = async (
    entity: PersonaApiResponse | LeadContactoApiResponse,
    type: 'persona' | 'leadContacto'
  ) => {
    let entityName = 'Nombre Desconocido';
    // Type guard to ensure 'nombre' property exists
    if (entity && typeof entity === 'object' && 'nombre' in entity && typeof entity.nombre === 'string') {
        entityName = entity.nombre;
    }
    // Type guard for 'apellidos'
    if (entity && typeof entity === 'object' && 'apellidos' in entity && entity.apellidos && typeof entity.apellidos === 'string') {
      entityName = `${entityName} ${entity.apellidos}`;
    }

    const newAssociatedEntity: AssociatedEntity = {
      id: entity.id,
      nombre: entityName.trim(),
      tipo: type,
    };

    // Add to local state first
    onAddAssociatedEntity(newAssociatedEntity); // This will also update potentialSpeakers via useMeetingProcessing hook

    // If we have a meeting ID, also associate with the reunion in the database
    if (meetingState.id && session?.access_token) {
      try {
        const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/reuniones/${meetingState.id}/associations`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${session.access_token}`,
          },
          body: JSON.stringify({
            entity_id: entity.id,
            entity_type: type,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ detail: `HTTP error ${response.status}` }));
          console.error('Failed to associate entity with reunion:', errorData);
          warning('Advertencia', `La entidad se creó correctamente pero no se pudo asociar con la reunión: ${errorData.detail || 'Error desconocido'}`);
        } else {
          console.log(`Successfully associated ${type} ${entity.id} with reunion ${meetingState.id}`);
        }
      } catch (error) {
        console.error('Error associating entity with reunion:', error);
        warning('Advertencia', 'La entidad se creó correctamente pero no se pudo asociar con la reunión debido a un error de conexión.');
      }
    }

    if (assigningSpeakerForTag) {
        const speakerEntity: SpeakerAssignable = {
            id: newAssociatedEntity.id,
            nombre: newAssociatedEntity.nombre,
            tipo: newAssociatedEntity.tipo as 'persona' | 'leadContacto',
        };
        onUpdateSpeakerAssignment(assigningSpeakerForTag, speakerEntity);
        setAssigningSpeakerForTag(null);
    }
    setActiveCrmModal(null);
  };

  const handleSubmitAssignments = async () => {
    if (!meetingState.id) {
      showError("Error de reunión", "No hay ID de reunión para asignar participantes.");
      return;
    }

    // Validar que se haya seleccionado si incluir video o no
    if (meetingState.video === null) {
      showError("Configuración requerida", "Debes seleccionar si incluir o no el video en el procesamiento.");
      return;
    }

    setIsSubmitting(true);
    const assignmentsPayload = Object.entries(meetingState.speakerAssignments)
      .filter(([, assignment]) => assignment.id && assignment.nombre)
      .map(([speakerTag, assignment]) => ({
        speaker_tag: speakerTag,
        asignado_a_id: assignment.id,
        asignado_a_tipo: assignment.tipo,
        nombre_asignado: assignment.nombre,
      }));

    if (assignmentsPayload.length === 0 && meetingState.identifiedSpeakers.length > 0) {
      const shouldContinue = confirm("No has asignado ningún participante. ¿Deseas continuar sin asignar?");
      if (!shouldContinue) {
        setIsSubmitting(false);
        warning("Asignación cancelada", "Puedes asignar participantes antes de continuar.");
        return;
      }
    }

    console.log("Submitting Speaker Assignments:", assignmentsPayload);

    if (!session?.access_token) {
      showError("Error de autenticación", "Por favor, inicie sesión de nuevo.");
      setIsSubmitting(false);
      return;
    }

    try {
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/reuniones/${meetingState.id}/speaker_assignments`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify(assignmentsPayload),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ detail: `HTTP error ${response.status}` }));
        throw new Error(errorData.detail || `Error al asignar participantes: ${response.statusText}`);
      }
      const result = await response.json();
      console.log("Participantes asignados, reunión actualizada:", result);

      // The useMeetingProcessing hook will handle status updates via Realtime/Polling
      // We just need to transition the step
      onSetCurrentStep(2.5); // Go to AI processing loading step
      success("Participantes asignados", "Procesando reunión con IA...");

    } catch (error) {
      console.error("Error submitting Step 2:", error);
      showError("Error al asignar participantes", error instanceof Error ? error.message : 'Error desconocido');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePlaySegment = (speakerId: string) => {
    if (!audioPlayerRef.current) return;

    if (currentPlayingSpeakerId === speakerId && !audioPlayerRef.current.paused) {
      audioPlayerRef.current.pause();
      if (playbackTimeoutRef.current) clearTimeout(playbackTimeoutRef.current);
      setCurrentPlayingSpeakerId(null);
      return;
    }

    if (!audioPlayerRef.current.paused) {
        audioPlayerRef.current.pause();
        if (playbackTimeoutRef.current) clearTimeout(playbackTimeoutRef.current);
    }

    const segmentInfo = meetingState.audioSegments.get(speakerId);
    if (segmentInfo) {
      setIsAudioLoading(true);
      setCurrentPlayingSpeakerId(speakerId);
      audioPlayerRef.current.src = segmentInfo.audioSrc;
      audioPlayerRef.current.currentTime = segmentInfo.startTime;
      audioPlayerRef.current.play().catch(e => {
        console.error("Error playing audio:", e);
        setIsAudioLoading(false);
        setCurrentPlayingSpeakerId(null);
      });

      if (playbackTimeoutRef.current) {
        clearTimeout(playbackTimeoutRef.current);
      }
      playbackTimeoutRef.current = window.setTimeout(() => {
        if (audioPlayerRef.current) {
          audioPlayerRef.current.pause();
        }
      }, segmentInfo.duration * 1000);
    }
  };

  useEffect(() => {
    const player = audioPlayerRef.current;
    if (!player) return;

    const onLoaded = () => setIsAudioLoading(false);
    const onPlay = () => setIsAudioLoading(false);
    const onPauseOrEnded = () => {
      setCurrentPlayingSpeakerId(null);
      setIsAudioLoading(false);
      if (playbackTimeoutRef.current) {
        clearTimeout(playbackTimeoutRef.current);
        playbackTimeoutRef.current = null;
      }
    };
    const onError = () => {
      console.error("Audio player error");
      setIsAudioLoading(false);
      setCurrentPlayingSpeakerId(null);
       if (playbackTimeoutRef.current) {
        clearTimeout(playbackTimeoutRef.current);
        playbackTimeoutRef.current = null;
      }
    };

    player.addEventListener('loadeddata', onLoaded);
    player.addEventListener('canplay', onLoaded);
    player.addEventListener('play', onPlay);
    player.addEventListener('pause', onPauseOrEnded);
    player.addEventListener('ended', onPauseOrEnded);
    player.addEventListener('error', onError);

    return () => {
      player.removeEventListener('loadeddata', onLoaded);
      player.removeEventListener('canplay', onLoaded);
      player.removeEventListener('play', onPlay);
      player.removeEventListener('pause', onPauseOrEnded);
      player.removeEventListener('ended', onPauseOrEnded);
      player.removeEventListener('error', onError);
      if (playbackTimeoutRef.current) {
        clearTimeout(playbackTimeoutRef.current);
      }
    };
  }, [audioPlayerRef, meetingState.audioSegments]);


  // --- Common Form Styling ---
  const inputClass = "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm disabled:bg-gray-100";
  const buttonClass = "px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50";
  const secondaryButtonClass = "px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50";

  const handleEntrevistaToggle = async (entrevista: boolean) => {
    try {
      await onUpdateEntrevista(entrevista);
      success('Actualizado', `Reunión marcada como ${entrevista ? 'entrevista' : 'reunión normal'}`);
    } catch (error) {
      console.error('Error updating entrevista:', error);
      showError('Error', 'No se pudo actualizar el tipo de reunión');
    }
  };

  const handleVideoToggle = async (video: boolean) => {
    try {
      await onUpdateVideo(video);
      success('Actualizado', `Video ${video ? 'incluido' : 'excluido'} del procesamiento`);
    } catch (error) {
      console.error('Error updating video:', error);
      showError('Error', 'No se pudo actualizar la configuración de video');
    }
  };

  return (
    <div className="space-y-6 bg-white p-6 shadow-md rounded-lg">
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-700 mb-4">Paso 2: Asignar Participantes</h2>

        {/* Configuration Section */}
        <div className="bg-gray-50 p-4 rounded-lg space-y-4">
          {/* Entrevista Toggle */}
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-600">Tipo de reunión:</span>
            <div className="flex items-center space-x-2">
              <button
                type="button"
                onClick={() => handleEntrevistaToggle(false)}
                className={`px-3 py-1 text-xs font-medium rounded-md transition-colors ${
                  meetingState.entrevista === false
                    ? 'bg-blue-100 text-blue-700 border border-blue-300'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
                disabled={isSubmitting}
              >
                Reunión
              </button>
              <button
                type="button"
                onClick={() => handleEntrevistaToggle(true)}
                className={`px-3 py-1 text-xs font-medium rounded-md transition-colors ${
                  meetingState.entrevista === true
                    ? 'bg-orange-100 text-orange-700 border border-orange-300'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
                disabled={isSubmitting}
              >
                Entrevista
              </button>
            </div>
          </div>

          {/* Video Toggle */}
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-600">Incluir video en procesamiento: <span className="text-red-500">*</span></span>
            <div className="flex items-center space-x-2">
              <button
                type="button"
                onClick={() => handleVideoToggle(false)}
                className={`px-3 py-1 text-xs font-medium rounded-md transition-colors ${
                  meetingState.video === false
                    ? 'bg-red-100 text-red-700 border border-red-300'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
                disabled={isSubmitting}
              >
                No incluir
              </button>
              <button
                type="button"
                onClick={() => handleVideoToggle(true)}
                className={`px-3 py-1 text-xs font-medium rounded-md transition-colors ${
                  meetingState.video === true
                    ? 'bg-green-100 text-green-700 border border-green-300'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
                disabled={isSubmitting}
              >
                Incluir video
              </button>
            </div>
          </div>

          {meetingState.video === null && (
            <p className="text-sm text-red-600 bg-red-50 p-2 rounded">
              ⚠️ Debes seleccionar si incluir o no el video antes de continuar.
            </p>
          )}
        </div>
      </div>

      {meetingState.rawTranscript ? (
        <div className="space-y-4">
          <div>
            <h3 className="text-md font-medium text-gray-700 mb-2">Transcripción (Participantes anónimos):</h3>
            <div
              className="p-3 bg-gray-50 rounded-md max-h-60 overflow-y-auto text-sm prose prose-sm max-w-none transcript-content-area"
              dangerouslySetInnerHTML={{ __html: meetingState.displayTranscript || "" }}
            />
          </div>

          {meetingState.identifiedSpeakers.length > 0 ? (
            <div>
              <h3 className="text-md font-medium text-gray-700 mb-3">Asignar a:</h3>
              {meetingState.identifiedSpeakers.map(speakerTag => (
                <div key={speakerTag} className="mb-3 p-3 border rounded-md bg-gray-50">
                  <label htmlFor={`speaker-${speakerTag}`} className="block text-sm font-medium text-gray-600 mb-1">
                    {speakerTag}:
                  </label>
                  <div className="flex items-center space-x-2 w-full">
                    <select
                      id={`speaker-${speakerTag}`}
                      value={meetingState.speakerAssignments[speakerTag]?.id || ""}
                      onChange={(e) => handleSpeakerAssignmentChange(speakerTag, e.target.value)}
                      className={`${inputClass} flex-grow`}
                      disabled={isSubmitting}
                    >
                      <option value="">Seleccionar participante...</option>
                      {meetingState.potentialSpeakers.map(speaker => (
                        <option key={`${speaker.tipo}-${speaker.id}`} value={speaker.id}>
                          {speaker.nombre} ({speaker.tipo === 'usuario' ? 'Yo (Uploader)' : speaker.tipo})
                        </option>
                      ))}
                    </select>
                    {meetingState.audioSegments.has(speakerTag) && (
                      <button
                        type="button"
                        onClick={() => handlePlaySegment(speakerTag)}
                        className={`p-1.5 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-blue-500 ${
                          currentPlayingSpeakerId === speakerTag ? 'bg-blue-500 text-white' : 'bg-blue-100 hover:bg-blue-200 text-blue-700'
                        }`}
                        aria-label={`Reproducir segmento de ${speakerTag}`}
                        disabled={isAudioLoading && currentPlayingSpeakerId !== speakerTag || isSubmitting}
                      >
                        {isAudioLoading && currentPlayingSpeakerId === speakerTag ? (
                          <LoadingSpinner size="small" />
                        ) : currentPlayingSpeakerId === speakerTag ? (
                          <StopIcon className="h-4 w-4" />
                        ) : (
                          <PlayIcon className="h-4 w-4" />
                        )}
                      </button>
                    )}
                    <button
                      type="button"
                      onClick={() => openSelectModalForSpeaker(speakerTag, 'persona')}
                      className="text-xs bg-blue-100 hover:bg-blue-200 text-blue-700 py-1 px-2 rounded disabled:opacity-50"
                      disabled={isSubmitting}
                    >
                      Persona
                    </button>
                    <button
                      type="button"
                      onClick={() => openCrmModalForSpeaker(speakerTag, 'persona')}
                      className="text-xs bg-green-100 hover:bg-green-200 text-green-700 py-1 px-2 rounded disabled:opacity-50"
                      disabled={isSubmitting}
                    >
                      + Nueva Persona
                    </button>
                    <button
                      type="button"
                      onClick={() => openSelectModalForSpeaker(speakerTag, 'leadContacto')}
                      className="text-xs bg-indigo-100 hover:bg-indigo-200 text-indigo-700 py-1 px-2 rounded disabled:opacity-50"
                      disabled={isSubmitting}
                    >
                      Lead Contacto
                    </button>
                    <button
                      type="button"
                      onClick={() => openCrmModalForSpeaker(speakerTag, 'leadContacto')}
                      className="text-xs bg-purple-100 hover:bg-purple-200 text-purple-700 py-1 px-2 rounded disabled:opacity-50"
                      disabled={isSubmitting}
                    >
                      + Nuevo Lead Contacto
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500 text-sm">No se identificaron tags de "Speaker X:" en la transcripción.</p>
          )}
        </div>
      ) : (
        <p className="text-gray-500">Cargando transcripción...</p>
      )}

      <div className="flex justify-between pt-4">
        <button
          type="button"
          onClick={() => {
            if(meetingState.id && !meetingState.rawTranscript) navigate('/meetings'); // If editing and somehow no transcript, go to list
            else onSetCurrentStep(1);
          }}
          className={secondaryButtonClass}
          disabled={isSubmitting}
        >
          Anterior
        </button>
        <button
          type="button"
          onClick={handleSubmitAssignments}
          className={buttonClass}
          disabled={isSubmitting || !meetingState.rawTranscript || meetingState.video === null} // Disable if no transcript or video not selected
        >
          {isSubmitting ? 'Procesando...' : 'Siguiente: Ver Resultados'}
        </button>
      </div>

      {/* CRM Modals for Speaker Assignment */}
      <Modal isOpen={activeCrmModal === 'persona'} onClose={() => setActiveCrmModal(null)} title="Crear Nueva Persona para Speaker" size="lg">
        <PersonaCreateForm onSubmitSuccess={(data) => handleCrmEntityCreated(data, 'persona')} onCancel={() => setActiveCrmModal(null)} />
      </Modal>
      <Modal isOpen={activeCrmModal === 'leadContacto'} onClose={() => setActiveCrmModal(null)} title="Crear Nuevo Lead Contacto para Speaker" size="lg">
        <LeadContactoCreateForm
            onSubmitSuccess={(data) => handleCrmEntityCreated(data, 'leadContacto')}
            onCancel={() => setActiveCrmModal(null)}
            openLeadEmpresaModal={() => { setActiveCrmModal(null); /* Potentially open another modal or handle differently */}}
        />
      </Modal>

      {/* Select Existing Entity Modal */}
      <SelectExistingEntityModal
        isOpen={!!activeSelectModal}
        onClose={() => setActiveSelectModal(null)}
        entityType={activeSelectModal}
        onEntitiesSelected={handleExistingEntitySelected}
        alreadyAssociatedIds={meetingState.associatedEntities.map(ae => ae.id)}
      />
    </div>
  );
};

export default SpeakerAssignmentStep;