import React, { useState } from 'react';
import {
  ChevronDownIcon,
  ChevronUpIcon,
  ClockIcon,
  UserIcon
} from '@heroicons/react/24/outline';
import {
  parseTranscriptSegments,
  getSpeakerColor,
  formatTimeRange,
  truncateContent,
  TranscriptSegment
} from '../../utils/transcriptFormatter';

interface TranscriptViewerProps {
  transcript: string;
  title?: string;
  isExpanded?: boolean;
  onToggleExpanded?: (expanded: boolean) => void;
  maxPreviewSegments?: number;
}

interface TranscriptSegmentProps {
  segment: TranscriptSegment;
  isPreview?: boolean;
}

const TranscriptSegmentComponent: React.FC<TranscriptSegmentProps> = ({
  segment,
  isPreview = false
}) => {
  const speakerColorClass = getSpeakerColor(segment.speakerId);
  const content = isPreview ? truncateContent(segment.content) : segment.content;

  return (
    <div className="container-info-speaker mb-4 p-4 bg-white rounded-lg border border-gray-200 shadow-sm">
      <div className="flex items-start space-x-3">
        {/* Time and Speaker Info */}
        <div className="flex-shrink-0 min-w-0">
          {segment.timeRange && (
            <div className="time-speaker flex items-center text-xs text-gray-500 mb-1">
              <ClockIcon className="h-3 w-3 mr-1" />
              <span>{formatTimeRange(segment.timeRange)}</span>
            </div>
          )}
          <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${speakerColorClass}`}>
            <UserIcon className="h-3 w-3 mr-1" />
            <span className="truncate max-w-24" title={segment.speaker}>
              {segment.speaker}
            </span>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <p
            className="text_speaker text-sm text-gray-700 leading-relaxed"
            id={segment.speakerId}
          >
            {content}
          </p>
        </div>
      </div>
    </div>
  );
};

const TranscriptViewer: React.FC<TranscriptViewerProps> = ({
  transcript,
  title = "Transcripción",
  isExpanded = false,
  onToggleExpanded,
  maxPreviewSegments = 3
}) => {
  const [internalExpanded, setInternalExpanded] = useState(isExpanded);

  const expanded = onToggleExpanded ? isExpanded : internalExpanded;
  const setExpanded = onToggleExpanded || setInternalExpanded;

  const segments = parseTranscriptSegments(transcript);
  const previewSegments = segments.slice(0, maxPreviewSegments);
  const hasMoreSegments = segments.length > maxPreviewSegments;

  const handleToggle = () => {
    setExpanded(!expanded);
  };

  if (!transcript) {
    return (
      <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
        <p className="text-gray-500 text-sm text-center">
          No hay transcripción disponible
        </p>
      </div>
    );
  }

  // If no segments were parsed, show raw transcript as fallback
  if (segments.length === 0) {
    return (
      <div className="transcript-viewer">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-800">{title}</h3>
          <button
            onClick={handleToggle}
            className="flex items-center space-x-1 text-sm text-gray-600 hover:text-gray-800 transition-colors"
          >
            <span>{expanded ? 'Contraer' : 'Expandir'}</span>
            {expanded ? (
              <ChevronUpIcon className="h-4 w-4" />
            ) : (
              <ChevronDownIcon className="h-4 w-4" />
            )}
          </button>
        </div>

        <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200 mb-4">
          <p className="text-yellow-700 text-sm">
            ⚠️ No se pudo parsear la transcripción. Mostrando contenido sin formato.
          </p>
        </div>

        <div className={`p-4 rounded-lg border bg-gray-50 border-gray-200 ${
          expanded ? 'max-h-96 overflow-y-auto' : 'max-h-32 overflow-hidden'
        }`}>
          <div
            className="text-sm text-gray-700 whitespace-pre-wrap"
            dangerouslySetInnerHTML={{ __html: transcript }}
          />
        </div>

        <div className="bg-gray-50 p-3 rounded-lg border border-gray-200 mt-3">
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>Contenido sin procesar</span>
            <span>Longitud: {transcript.length} caracteres</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="transcript-viewer">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-800">{title}</h3>
        <button
          onClick={handleToggle}
          className="flex items-center space-x-1 text-sm text-gray-600 hover:text-gray-800 transition-colors"
        >
          <span>{expanded ? 'Contraer' : 'Expandir'}</span>
          {expanded ? (
            <ChevronUpIcon className="h-4 w-4" />
          ) : (
            <ChevronDownIcon className="h-4 w-4" />
          )}
        </button>
      </div>

      {/* Content */}
      {!expanded ? (
        /* Preview Mode */
        <div className="space-y-3">
          {previewSegments.map((segment, index) => (
            <TranscriptSegmentComponent
              key={index}
              segment={segment}
              isPreview={true}
            />
          ))}

          {hasMoreSegments && (
            <div className="text-center py-3">
              <button
                onClick={handleToggle}
                className="text-sm text-indigo-600 hover:text-indigo-800 font-medium"
              >
                Ver {segments.length - maxPreviewSegments} segmentos más...
              </button>
            </div>
          )}

          <div className="bg-gray-50 p-3 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between text-xs text-gray-500">
              <span>Total de segmentos: {segments.length}</span>
              <span>Longitud: {transcript.length} caracteres</span>
            </div>
          </div>
        </div>
      ) : (
        /* Expanded Mode */
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {segments.map((segment, index) => (
            <TranscriptSegmentComponent
              key={index}
              segment={segment}
              isPreview={false}
            />
          ))}

          <div className="bg-gray-50 p-3 rounded-lg border border-gray-200 sticky bottom-0">
            <div className="flex items-center justify-between text-xs text-gray-500">
              <span>Total de segmentos: {segments.length}</span>
              <span>Longitud: {transcript.length} caracteres</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TranscriptViewer;
