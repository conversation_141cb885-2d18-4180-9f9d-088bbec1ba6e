import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useNavigate, useParams } from 'react-router-dom';
import { useProyectos } from '../../hooks/useTasks';
import { TareaUpdate, ESTADOS_TAREA, PRIORIDADES_TAREA, URGENCIAS_TAREA, Tarea } from '../../types/tarea';
import { apiClient } from '../../lib/api';
import {
  Save,
  X,
  Calendar,
  FolderOpen,
  AlertTriangle,
  Clock,
  Loader
} from 'lucide-react';

// Validation schema
const tareaEditSchema = z.object({
  titulo: z.string().min(1, 'El título es requerido').max(255, 'El título es muy largo'),
  descripcion: z.string().optional(),
  proyecto_id: z.string().optional(),
  estado: z.enum(['Pendiente', 'En Progreso', 'En Revisión', 'Bloqueada', 'Completada']).optional(),
  prioridad: z.enum(['Baja', 'Media', 'Alta', 'Urgente']).optional(),
  urgencia: z.enum(['Urgente', 'No Urgente']).optional(),
  fecha_vencimiento: z.string().optional(),
  asignado_a: z.string().optional(),
  tarea_padre_id: z.string().optional(),
  info_adicional: z.string().optional(),
});

type TareaEditFormValues = z.infer<typeof tareaEditSchema>;

const TaskEditForm: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  
  const { proyectos } = useProyectos();

  const [task, setTask] = useState<Tarea | null>(null);
  const [loading, setLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<TareaEditFormValues>({
    resolver: zodResolver(tareaEditSchema),
  });

  // Fetch task data
  useEffect(() => {
    if (id) {
      fetchTask(id);
    }
  }, [id]);

  const fetchTask = async (taskId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.tareas.getById(taskId);
      const taskData = response as Tarea;
      setTask(taskData);
      
      // Populate form with task data
      reset({
        titulo: taskData.titulo,
        descripcion: taskData.descripcion || '',
        proyecto_id: taskData.proyecto_id || '',
        estado: taskData.estado,
        prioridad: taskData.prioridad,
        urgencia: taskData.urgencia,
        fecha_vencimiento: taskData.fecha_vencimiento || '',
        asignado_a: taskData.asignado_a || '',
        tarea_padre_id: taskData.tarea_padre_id || '',
        info_adicional: taskData.info_adicional || '',
      });
    } catch (err: any) {
      console.error('Error fetching task:', err);
      setError(err.message || 'Error al cargar la tarea');
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data: TareaEditFormValues) => {
    if (!id) return;

    try {
      setIsSubmitting(true);
      setError(null);

      // Convert form data to API format
      const updateData: TareaUpdate = {
        titulo: data.titulo,
        descripcion: data.descripcion || undefined,
        proyecto_id: data.proyecto_id || undefined,
        estado: data.estado as any,
        prioridad: data.prioridad as any,
        urgencia: data.urgencia as any,
        fecha_vencimiento: data.fecha_vencimiento || undefined,
        asignado_a: data.asignado_a || undefined,
        tarea_padre_id: data.tarea_padre_id || undefined,
        info_adicional: data.info_adicional || undefined,
      };

      await apiClient.tareas.update(id, updateData);
      
      // Navigate back to task details
      navigate(`/tareas/${id}`);
    } catch (err: any) {
      console.error('Error updating task:', err);
      setError(err.message || 'Error al actualizar la tarea');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    navigate(`/tareas/${id}`);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Cargando tarea...</p>
        </div>
      </div>
    );
  }

  if (error && !task) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-red-500" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Error al cargar la tarea</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => navigate('/tareas')}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Volver a Tareas
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Editar Tarea</h1>
            <p className="text-gray-600 mt-1">
              Modifica los detalles de la tarea
            </p>
          </div>
          
          <button
            onClick={handleCancel}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <X className="h-4 w-4 mr-2" />
            Cancelar
          </button>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center">
              <AlertTriangle className="h-5 w-5 text-red-500 mr-2" />
              <p className="text-red-700">{error}</p>
            </div>
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-6">Información Básica</h2>
            
            <div className="grid grid-cols-1 gap-6">
              {/* Title */}
              <div>
                <label htmlFor="titulo" className="block text-sm font-medium text-gray-700 mb-2">
                  Título *
                </label>
                <input
                  {...register('titulo')}
                  type="text"
                  id="titulo"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Ingresa el título de la tarea"
                />
                {errors.titulo && (
                  <p className="mt-1 text-sm text-red-600">{errors.titulo.message}</p>
                )}
              </div>

              {/* Description */}
              <div>
                <label htmlFor="descripcion" className="block text-sm font-medium text-gray-700 mb-2">
                  Descripción
                </label>
                <textarea
                  {...register('descripcion')}
                  id="descripcion"
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Describe los detalles de la tarea"
                />
                {errors.descripcion && (
                  <p className="mt-1 text-sm text-red-600">{errors.descripcion.message}</p>
                )}
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-6">Configuración</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Project */}
              <div>
                <label htmlFor="proyecto_id" className="block text-sm font-medium text-gray-700 mb-2">
                  <FolderOpen className="h-4 w-4 inline mr-1" />
                  Proyecto
                </label>
                <select
                  {...register('proyecto_id')}
                  id="proyecto_id"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Seleccionar proyecto</option>
                  {proyectos.map((proyecto) => (
                    <option key={proyecto.id} value={proyecto.id}>
                      {proyecto.nombre}
                    </option>
                  ))}
                </select>
              </div>

              {/* Status */}
              <div>
                <label htmlFor="estado" className="block text-sm font-medium text-gray-700 mb-2">
                  Estado
                </label>
                <select
                  {...register('estado')}
                  id="estado"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {ESTADOS_TAREA.map((estado) => (
                    <option key={estado} value={estado}>
                      {estado}
                    </option>
                  ))}
                </select>
              </div>

              {/* Priority */}
              <div>
                <label htmlFor="prioridad" className="block text-sm font-medium text-gray-700 mb-2">
                  Prioridad
                </label>
                <select
                  {...register('prioridad')}
                  id="prioridad"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {PRIORIDADES_TAREA.map((prioridad) => (
                    <option key={prioridad} value={prioridad}>
                      {prioridad}
                    </option>
                  ))}
                </select>
              </div>

              {/* Urgency */}
              <div>
                <label htmlFor="urgencia" className="block text-sm font-medium text-gray-700 mb-2">
                  <Clock className="h-4 w-4 inline mr-1" />
                  Urgencia
                </label>
                <select
                  {...register('urgencia')}
                  id="urgencia"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {URGENCIAS_TAREA.map((urgencia) => (
                    <option key={urgencia} value={urgencia}>
                      {urgencia}
                    </option>
                  ))}
                </select>
              </div>

              {/* Due Date */}
              <div>
                <label htmlFor="fecha_vencimiento" className="block text-sm font-medium text-gray-700 mb-2">
                  <Calendar className="h-4 w-4 inline mr-1" />
                  Fecha de Vencimiento
                </label>
                <input
                  {...register('fecha_vencimiento')}
                  type="date"
                  id="fecha_vencimiento"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* Additional Info */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-6">Información Adicional</h2>
            
            <div>
              <label htmlFor="info_adicional" className="block text-sm font-medium text-gray-700 mb-2">
                Notas Adicionales
              </label>
              <textarea
                {...register('info_adicional')}
                id="info_adicional"
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Información adicional sobre la tarea"
              />
            </div>
          </div>

          {/* Submit Buttons */}
          <div className="flex items-center justify-end space-x-4">
            <button
              type="button"
              onClick={handleCancel}
              className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancelar
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="inline-flex items-center px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Guardando...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Guardar Cambios
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default TaskEditForm;
