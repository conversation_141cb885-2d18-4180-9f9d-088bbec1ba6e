import React, { useState } from 'react';
import { TareaKanbanBoard, TareaSummary, ESTADO_TAREA_COLORS } from '../../types/tarea';
import { Calendar, User, AlertTriangle, Clock, MoreVertical, Trash2 } from 'lucide-react';

interface TaskKanbanProps {
  kanbanBoard: TareaKanbanBoard;
  onTaskClick: (taskId: string) => void;
  onUpdateTask: (taskId: string, field: string, value: any) => Promise<void>;
  onDeleteTask: (taskId: string, taskTitle: string) => Promise<void>;
}

export const TaskKanban: React.FC<TaskKanbanProps> = ({
  kanbanBoard,
  onTaskClick,
  onUpdateTask,
  onDeleteTask
}) => {
  const [draggedTask, setDraggedTask] = useState<TareaSummary | null>(null);

  const handleDragStart = (e: React.DragEvent, task: TareaSummary) => {
    setDraggedTask(task);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = async (e: React.DragEvent, newEstado: string) => {
    e.preventDefault();
    
    if (draggedTask && draggedTask.estado !== newEstado) {
      try {
        await onUpdateTask(draggedTask.id, 'estado', newEstado);
      } catch (error) {
        console.error('Error updating task status:', error);
      }
    }
    
    setDraggedTask(null);
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('es-ES');
  };

  const getDaysText = (days?: number) => {
    if (days === undefined) return '';
    if (days < 0) return `Vencida hace ${Math.abs(days)} días`;
    if (days === 0) return 'Vence hoy';
    return `Vence en ${days} días`;
  };

  const TaskCard: React.FC<{ task: TareaSummary }> = ({ task }) => {
    const [showMenu, setShowMenu] = useState(false);

    return (
      <div
        draggable
        onDragStart={(e) => handleDragStart(e, task)}
        className={`
          bg-white rounded-lg border p-3 mb-3 cursor-move hover:shadow-md transition-all
          ${task.es_vencida ? 'border-red-200 bg-red-50' : 'border-gray-200 hover:border-gray-300'}
          ${draggedTask?.id === task.id ? 'opacity-50' : ''}
        `}
      >
        {/* Header */}
        <div className="flex items-start justify-between mb-2">
          <h4 
            className="text-sm font-medium text-gray-900 line-clamp-2 flex-1 cursor-pointer hover:text-blue-600"
            onClick={() => onTaskClick(task.id)}
          >
            {task.titulo}
          </h4>
          <div className="flex items-center space-x-1 ml-2">
            {task.es_vencida && (
              <AlertTriangle className="h-4 w-4 text-red-500 flex-shrink-0" />
            )}
            <div className="relative">
              <button
                onClick={() => setShowMenu(!showMenu)}
                className="p-1 text-gray-400 hover:text-gray-600 rounded"
              >
                <MoreVertical className="h-3 w-3" />
              </button>
              {showMenu && (
                <div className="absolute right-0 top-6 bg-white border border-gray-200 rounded-md shadow-lg z-10 min-w-[120px]">
                  <button
                    onClick={() => {
                      onTaskClick(task.id);
                      setShowMenu(false);
                    }}
                    className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50"
                  >
                    Ver detalles
                  </button>
                  <button
                    onClick={() => {
                      onDeleteTask(task.id, task.titulo);
                      setShowMenu(false);
                    }}
                    className="w-full text-left px-3 py-2 text-sm text-red-600 hover:bg-red-50"
                  >
                    <Trash2 className="h-3 w-3 inline mr-2" />
                    Eliminar
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Priority and Urgency */}
        <div className="flex items-center space-x-2 mb-2">
          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
            task.prioridad === 'Urgente' ? 'bg-red-100 text-red-800' :
            task.prioridad === 'Alta' ? 'bg-orange-100 text-orange-800' :
            task.prioridad === 'Media' ? 'bg-yellow-100 text-yellow-800' :
            'bg-gray-100 text-gray-800'
          }`}>
            {task.prioridad}
          </span>
          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
            task.urgencia === 'Urgente' ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
          }`}>
            {task.urgencia}
          </span>
        </div>

        {/* Footer */}
        <div className="space-y-2">
          {/* Assigned user */}
          {task.asignado_nombre && (
            <div className="flex items-center text-xs text-gray-600">
              <User className="h-3 w-3 mr-1" />
              <span>{task.asignado_nombre}</span>
            </div>
          )}

          {/* Due date */}
          {task.fecha_vencimiento && (
            <div className={`flex items-center text-xs ${
              task.es_vencida ? 'text-red-600' : 'text-gray-600'
            }`}>
              <Calendar className="h-3 w-3 mr-1" />
              <div>
                <div>{formatDate(task.fecha_vencimiento)}</div>
                {task.dias_vencimiento !== undefined && (
                  <div className="text-xs">
                    {getDaysText(task.dias_vencimiento)}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Project */}
          {task.proyecto_nombre && (
            <div className="text-xs text-gray-500 truncate">
              📁 {task.proyecto_nombre}
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="flex space-x-6 overflow-x-auto pb-6" style={{ minWidth: '1200px' }}>
      {kanbanBoard.columnas.map((columna) => (
        <div
          key={columna.estado}
          className="flex-shrink-0 w-80"
          onDragOver={handleDragOver}
          onDrop={(e) => handleDrop(e, columna.estado)}
        >
          {/* Column Header */}
          <div className="bg-white rounded-lg border border-gray-200 mb-4">
            <div className={`px-4 py-3 border-b border-gray-200 rounded-t-lg ${ESTADO_TAREA_COLORS[columna.estado]}`}>
              <div className="flex items-center justify-between">
                <h3 className="font-semibold text-sm">{columna.estado}</h3>
                <span className="text-xs font-medium bg-white bg-opacity-20 px-2 py-1 rounded-full">
                  {columna.total}
                </span>
              </div>
            </div>

            {/* Column Content */}
            <div className="p-4 space-y-3 max-h-96 overflow-y-auto min-h-[200px]">
              {columna.tareas.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Clock className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No hay tareas</p>
                  <p className="text-xs text-gray-400 mt-1">
                    Arrastra tareas aquí
                  </p>
                </div>
              ) : (
                columna.tareas.map((task) => (
                  <TaskCard key={task.id} task={task} />
                ))
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};
