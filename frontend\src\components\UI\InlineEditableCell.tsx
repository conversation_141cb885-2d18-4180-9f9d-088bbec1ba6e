import React, { useState, useRef, useEffect } from 'react';

interface SelectOption {
  value: string;
  label: string;
}

interface InlineEditableCellProps {
  value: string | number | boolean | null;
  type: 'text' | 'number' | 'select' | 'date' | 'email' | 'tel' | 'checkbox';
  options?: SelectOption[];
  onSave: (value: any) => Promise<void> | void;
  className?: string;
  renderValue?: (value: any) => React.ReactNode;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  min?: number;
  max?: number;
  validation?: (value: any) => string | null;
  autoSave?: boolean;
}

export const InlineEditableCell: React.FC<InlineEditableCellProps> = ({
  value,
  type,
  options = [],
  onSave,
  className = '',
  renderValue,
  placeholder,
  disabled = false,
  required = false,
  min,
  max,
  validation,
  autoSave = true,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(value);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const inputRef = useRef<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>(null);

  // Update edit value when prop value changes
  useEffect(() => {
    if (!isEditing) {
      setEditValue(value);
    }
  }, [value, isEditing]);

  // Focus input when editing starts
  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      if (type === 'text' || type === 'email' || type === 'tel') {
        (inputRef.current as HTMLInputElement).select();
      }
    }
  }, [isEditing, type]);

  const handleStartEdit = () => {
    if (disabled) return;
    setIsEditing(true);
    setError(null);
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditValue(value);
    setError(null);
  };

  const handleSave = async () => {
    // Validación personalizada
    if (validation) {
      const validationError = validation(editValue);
      if (validationError) {
        setError(validationError);
        return;
      }
    }

    // Validación requerida
    if (required && (!editValue || (typeof editValue === 'string' && editValue.trim() === ''))) {
      setError('Campo requerido');
      return;
    }

    if (editValue === value) {
      setIsEditing(false);
      return;
    }

    setIsSaving(true);
    setError(null);

    try {
      let valueToSave = editValue;
      
      // Conversión de tipos según el tipo de campo
      if (type === 'number' && typeof editValue === 'string') {
        valueToSave = editValue === '' ? null : Number(editValue);
      } else if (type === 'checkbox') {
        valueToSave = Boolean(editValue);
      }

      await onSave(valueToSave);
      setIsEditing(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error al guardar');
    } finally {
      setIsSaving(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSave();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancel();
    }
  };

  const handleBlur = () => {
    if (autoSave && isEditing && !error) {
      handleSave();
    }
  };

  const renderInput = () => {
    const commonProps = {
      ref: inputRef as any,
      value: editValue || '',
      onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const newValue = e.target.type === 'checkbox' ? e.target.checked : e.target.value;
        setEditValue(newValue);
        setError(null);
      },
      onKeyDown: handleKeyDown,
      onBlur: handleBlur,
      className: `w-full px-2 py-1 text-sm border border-blue-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 ${
        error ? 'border-red-300' : ''
      }`,
      placeholder,
      required,
    };

    switch (type) {
      case 'number':
        return (
          <input
            {...commonProps}
            type="number"
            min={min}
            max={max}
          />
        );
      case 'date':
        return (
          <input
            {...commonProps}
            type="date"
          />
        );
      case 'email':
        return (
          <input
            {...commonProps}
            type="email"
          />
        );
      case 'tel':
        return (
          <input
            {...commonProps}
            type="tel"
          />
        );
      case 'checkbox':
        return (
          <input
            {...commonProps}
            type="checkbox"
            checked={Boolean(editValue)}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
        );
      case 'select':
        return (
          <select {...commonProps}>
            <option value="">Seleccionar...</option>
            {options.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );
      default:
        return <input {...commonProps} type="text" />;
    }
  };

  const renderDisplayValue = () => {
    if (renderValue) {
      return renderValue(value);
    }

    // Manejo de diferentes tipos de valores
    if (type === 'checkbox') {
      return (
        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
          value ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
        }`}>
          {value ? 'Sí' : 'No'}
        </span>
      );
    }

    if (type === 'date' && value) {
      const date = new Date(value as string);
      return <span className="text-sm">{date.toLocaleDateString('es-ES')}</span>;
    }

    if (!value || (typeof value === 'string' && value.trim() === '')) {
      return <span className="text-gray-400 italic text-sm">{placeholder || 'Sin valor'}</span>;
    }

    return <span className="text-sm">{String(value)}</span>;
  };

  if (isEditing) {
    return (
      <div className={`relative ${className}`}>
        {renderInput()}
        {error && (
          <div className="absolute top-full left-0 mt-1 text-xs text-red-600 bg-white border border-red-200 rounded px-2 py-1 shadow-sm z-10">
            {error}
          </div>
        )}
        {isSaving && (
          <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div
      onDoubleClick={handleStartEdit}
      className={`cursor-pointer p-2 rounded hover:bg-gray-50 transition-colors min-h-[2rem] flex items-center ${
        disabled ? 'cursor-not-allowed opacity-50' : ''
      } ${className}`}
      title="Doble clic para editar"
    >
      {renderDisplayValue()}
    </div>
  );
};
