/**
 * PestanaGeneralEmpresa - General tab component for company details
 * Shows comprehensive company information and statistics
 */

import React, { useState, useEffect } from 'react';
import { supabase } from '../../../services/supabaseClient';
import { apiClient } from '../../../lib/api';
import LoadingSpinner from '../../UI/LoadingSpinner';
import type { EmpresaGeneralDetails } from '../../../types/empresa';
import {
  Building2,
  Users,
  Calendar,
  BarChart3,
  TrendingUp,
  AlertCircle,
  ChevronDown,
  ChevronUp,
  MapPin,
  Phone,
  Mail,
  Globe
} from 'lucide-react';

interface PestanaGeneralEmpresaProps {
  empresaId: string;
}

const PestanaGeneralEmpresa: React.FC<PestanaGeneralEmpresaProps> = ({ empresaId }) => {
  const [data, setData] = useState<EmpresaGeneralDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAdditionalInfo, setShowAdditionalInfo] = useState(false);

  useEffect(() => {
    const fetchGeneralDetails = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await apiClient.empresas.getGeneralDetails(empresaId);
        setData(response);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Error loading general details';
        setError(errorMessage);
        console.error('Error fetching empresa general details:', err);
      } finally {
        setLoading(false);
      }
    };

    if (empresaId) {
      fetchGeneralDetails();
    }
  }, [empresaId]);

  // Real-time subscription for general company data
  useEffect(() => {
    if (!empresaId) return;

    console.log('Setting up General realtime subscription for empresa:', empresaId);

    let empresasSubscription: any = null;
    let personasSubscription: any = null;
    let proyectosSubscription: any = null;
    let departamentosSubscription: any = null;

    const setupRealtimeSubscriptions = () => {
      // Subscribe to empresas table changes
      empresasSubscription = supabase
        .channel(`empresas_general_${empresaId}`)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'empresas',
            filter: `id=eq.${empresaId}`,
          },
          (payload) => {
            console.log('Empresas real-time update:', payload);
            // Refetch data when company info changes
            const fetchGeneralDetails = async () => {
              try {
                const response = await apiClient.empresas.getGeneralDetails(empresaId);
                setData(response);
              } catch (err) {
                console.error('Error refetching general details:', err);
              }
            };
            fetchGeneralDetails();
          }
        )
        .subscribe((status) => {
          console.log('Empresas subscription status:', status);
        });

      // Subscribe to personas table changes (affects statistics)
      personasSubscription = supabase
        .channel(`personas_general_${empresaId}`)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'personas',
            filter: `empresa_id=eq.${empresaId}`,
          },
          (payload) => {
            console.log('Personas real-time update:', payload);
            // Refetch data when personas change
            const fetchGeneralDetails = async () => {
              try {
                const response = await apiClient.empresas.getGeneralDetails(empresaId);
                setData(response);
              } catch (err) {
                console.error('Error refetching general details:', err);
              }
            };
            fetchGeneralDetails();
          }
        )
        .subscribe((status) => {
          console.log('Personas subscription status:', status);
        });

      // Subscribe to proyectos table changes
      proyectosSubscription = supabase
        .channel(`proyectos_general_${empresaId}`)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'proyectos',
          },
          (payload) => {
            console.log('Proyectos real-time update:', payload);
            // Refetch data when projects change
            const fetchGeneralDetails = async () => {
              try {
                const response = await apiClient.empresas.getGeneralDetails(empresaId);
                setData(response);
              } catch (err) {
                console.error('Error refetching general details:', err);
              }
            };
            fetchGeneralDetails();
          }
        )
        .subscribe((status) => {
          console.log('Proyectos subscription status:', status);
        });

      // Subscribe to departamentos table changes
      departamentosSubscription = supabase
        .channel(`departamentos_general_${empresaId}`)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'departamentos',
            filter: `empresa_id=eq.${empresaId}`,
          },
          (payload) => {
            console.log('Departamentos real-time update:', payload);
            // Refetch data when departments change
            const fetchGeneralDetails = async () => {
              try {
                const response = await apiClient.empresas.getGeneralDetails(empresaId);
                setData(response);
              } catch (err) {
                console.error('Error refetching general details:', err);
              }
            };
            fetchGeneralDetails();
          }
        )
        .subscribe((status) => {
          console.log('Departamentos subscription status:', status);
        });
    };

    setupRealtimeSubscriptions();

    return () => {
      if (empresasSubscription) {
        console.log('Cleaning up Empresas realtime subscription');
        supabase.removeChannel(empresasSubscription);
      }
      if (personasSubscription) {
        console.log('Cleaning up Personas realtime subscription');
        supabase.removeChannel(personasSubscription);
      }
      if (proyectosSubscription) {
        console.log('Cleaning up Proyectos realtime subscription');
        supabase.removeChannel(proyectosSubscription);
      }
      if (departamentosSubscription) {
        console.log('Cleaning up Departamentos realtime subscription');
        supabase.removeChannel(departamentosSubscription);
      }
    };
  }, [empresaId]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <div className="flex">
          <AlertCircle className="h-5 w-5 text-red-400" />
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error al cargar los datos</h3>
            <p className="text-sm text-red-700 mt-1">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No se encontraron datos para esta empresa.</p>
      </div>
    );
  }

  // Safe destructuring with fallbacks to prevent undefined errors
  const {
    empresa,
    departamentos = [],
    proyectos_relacionados = [],
    total_trabajadores_activos = 0
  } = data;
  const {
    estadisticas_reuniones,
    estadisticas_procesos,
    distribucion_hallazgos = [],
    panel_personas
  } = data;

  return (
    <div className="space-y-6">
      {/* Main Information Section */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <Building2 className="h-5 w-5 mr-2" />
          Información Principal
        </h3>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Company Basic Info */}
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">Información Básica</h4>
              <div className="space-y-2">
                <div className="flex items-center text-sm">
                  <Building2 className="h-4 w-4 text-gray-400 mr-2" />
                  <span className="font-medium">{empresa.nombre}</span>
                </div>
                {empresa.sector && (
                  <div className="flex items-center text-sm text-gray-600">
                    <span className="w-4 mr-2"></span>
                    <span>Sector: {empresa.sector}</span>
                  </div>
                )}
                {empresa.nif_cif && (
                  <div className="flex items-center text-sm text-gray-600">
                    <span className="w-4 mr-2"></span>
                    <span>NIF/CIF: {empresa.nif_cif}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Contact Information */}
            {(empresa.telefono || empresa.email_principal || empresa.website || empresa.direccion) && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Contacto</h4>
                <div className="space-y-2">
                  {empresa.telefono && (
                    <div className="flex items-center text-sm text-gray-600">
                      <Phone className="h-4 w-4 text-gray-400 mr-2" />
                      <span>{empresa.telefono}</span>
                    </div>
                  )}
                  {empresa.email_principal && (
                    <div className="flex items-center text-sm text-gray-600">
                      <Mail className="h-4 w-4 text-gray-400 mr-2" />
                      <span>{empresa.email_principal}</span>
                    </div>
                  )}
                  {empresa.website && (
                    <div className="flex items-center text-sm text-gray-600">
                      <Globe className="h-4 w-4 text-gray-400 mr-2" />
                      <a href={empresa.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800">
                        {empresa.website}
                      </a>
                    </div>
                  )}
                  {empresa.direccion && (
                    <div className="flex items-center text-sm text-gray-600">
                      <MapPin className="h-4 w-4 text-gray-400 mr-2" />
                      <span>{empresa.direccion}</span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Description */}
            {empresa.descripcion && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Descripción</h4>
                <p className="text-sm text-gray-600">{empresa.descripcion}</p>
              </div>
            )}

            {/* Additional Info (Collapsible) */}
            {empresa.info_adicional && (
              <div>
                <button
                  onClick={() => setShowAdditionalInfo(!showAdditionalInfo)}
                  className="flex items-center text-sm font-medium text-gray-700 hover:text-gray-900"
                >
                  Información Adicional
                  {showAdditionalInfo ? (
                    <ChevronUp className="h-4 w-4 ml-1" />
                  ) : (
                    <ChevronDown className="h-4 w-4 ml-1" />
                  )}
                </button>
                {showAdditionalInfo && (
                  <div className="mt-2 p-3 bg-gray-50 rounded-md">
                    <p className="text-sm text-gray-600 whitespace-pre-wrap">{empresa.info_adicional}</p>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Departments and Workers */}
          <div className="space-y-4">
            {/* Workers Count */}
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-blue-600" />
                <div className="ml-3">
                  <p className="text-sm font-medium text-blue-900">Trabajadores Activos</p>
                  <p className="text-2xl font-bold text-blue-600">{total_trabajadores_activos}</p>
                </div>
              </div>
            </div>

            {/* Departments */}
            {departamentos.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Departamentos</h4>
                <div className="space-y-2">
                  {departamentos.map((dept) => (
                    <div key={dept.id} className="flex items-center p-2 bg-gray-50 rounded-md">
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">{dept.nombre}</p>
                        {dept.descripcion && (
                          <p className="text-xs text-gray-600">{dept.descripcion}</p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Projects Section */}
      {proyectos_relacionados.length > 0 && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <TrendingUp className="h-5 w-5 mr-2" />
            Proyectos Relacionados
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {proyectos_relacionados.map((proyecto) => (
              <div key={proyecto.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-start justify-between mb-2">
                  <h4 className="text-sm font-medium text-gray-900 line-clamp-2">{proyecto.nombre}</h4>
                  {proyecto.estado && (
                    <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                      {proyecto.estado}
                    </span>
                  )}
                </div>
                
                {proyecto.descripcion && (
                  <p className="text-xs text-gray-600 mb-3 line-clamp-2">{proyecto.descripcion}</p>
                )}
                
                {/* Progress Bar */}
                <div className="mb-2">
                  <div className="flex items-center justify-between text-xs text-gray-600 mb-1">
                    <span>Progreso</span>
                    <span>{Math.round(proyecto.progreso)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${Math.min(100, Math.max(0, proyecto.progreso))}%` }}
                    ></div>
                  </div>
                </div>
                
                {/* Dates */}
                {(proyecto.fecha_inicio || proyecto.fecha_fin_estimada) && (
                  <div className="text-xs text-gray-500 space-y-1">
                    {proyecto.fecha_inicio && (
                      <div className="flex items-center">
                        <Calendar className="h-3 w-3 mr-1" />
                        <span>Inicio: {new Date(proyecto.fecha_inicio).toLocaleDateString('es-ES')}</span>
                      </div>
                    )}
                    {proyecto.fecha_fin_estimada && (
                      <div className="flex items-center">
                        <Calendar className="h-3 w-3 mr-1" />
                        <span>Fin estimado: {new Date(proyecto.fecha_fin_estimada).toLocaleDateString('es-ES')}</span>
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Statistics Panel */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-6 flex items-center">
          <BarChart3 className="h-5 w-5 mr-2" />
          Panel de Estadísticas
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Meetings vs Interviews */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-700 mb-3">Reuniones vs Entrevistas</h4>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-600">Reuniones</span>
                <span className="text-sm font-semibold text-blue-600">{estadisticas_reuniones.total_reuniones}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-600">Entrevistas</span>
                <span className="text-sm font-semibold text-indigo-600">{estadisticas_reuniones.total_entrevistas}</span>
              </div>
              <div className="pt-2 border-t border-gray-200">
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-600">% Entrevistas</span>
                  <span className="text-sm font-bold text-indigo-700">
                    {Math.round(estadisticas_reuniones.reuniones_vs_entrevistas_ratio)}%
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Processes and Tasks */}
          <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-700 mb-3">Procesos y Tareas</h4>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-600">Procesos</span>
                <span className="text-sm font-semibold text-green-600">{estadisticas_procesos.total_procesos_clientes}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-600">Tareas</span>
                <span className="text-sm font-semibold text-emerald-600">{estadisticas_procesos.total_tareas_clientes}</span>
              </div>
              <div className="pt-2 border-t border-gray-200">
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-600">Tareas/Proceso</span>
                  <span className="text-sm font-bold text-emerald-700">
                    {estadisticas_procesos.total_procesos_clientes > 0
                      ? Math.round(estadisticas_procesos.total_tareas_clientes / estadisticas_procesos.total_procesos_clientes * 10) / 10
                      : 0}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* People Panel */}
          <div className="bg-gradient-to-r from-purple-50 to-violet-50 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-700 mb-3">Panel de Personas</h4>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-600">Total Activas</span>
                <span className="text-sm font-semibold text-purple-600">{panel_personas.total_personas_activas}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-600">Entrevistadas</span>
                <span className="text-sm font-semibold text-violet-600">{panel_personas.total_personas_entrevistadas}</span>
              </div>
              <div className="pt-2 border-t border-gray-200">
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-600">% Entrevistadas</span>
                  <span className="text-sm font-bold text-violet-700">
                    {Math.round(panel_personas.porcentaje_entrevistadas)}%
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Findings Distribution */}
          <div className="bg-gradient-to-r from-orange-50 to-amber-50 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-700 mb-3">Distribución de Hallazgos</h4>
            {distribucion_hallazgos.length > 0 ? (
              <div className="space-y-2">
                {distribucion_hallazgos.slice(0, 3).map((hallazgo, index) => (
                  <div key={index} className="space-y-1">
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-600 truncate">{hallazgo.tipo}</span>
                      <span className="text-xs font-semibold text-orange-600">{hallazgo.count}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-1">
                      <div
                        className="bg-orange-500 h-1 rounded-full transition-all duration-300"
                        style={{ width: `${Math.min(100, Math.max(0, hallazgo.percentage))}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
                {distribucion_hallazgos.length > 3 && (
                  <div className="text-xs text-gray-500 text-center pt-1">
                    +{distribucion_hallazgos.length - 3} más
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-4">
                <AlertCircle className="h-6 w-6 text-gray-400 mx-auto mb-1" />
                <p className="text-xs text-gray-500">Sin hallazgos</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PestanaGeneralEmpresa;
