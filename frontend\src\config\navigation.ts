// Navigation Configuration
// This file centralizes all navigation structure for the application
// It automatically validates routes and provides a single source of truth

export interface NavigationItem {
  path: string;
  icon: string;
  label: string;
  description?: string;
  requiresAuth?: boolean;
  roles?: string[]; // Future: role-based access
}

export interface NavigationGroup {
  id: string;
  label: string;
  icon: string;
  items: NavigationItem[];
}

export interface NavigationConfig {
  main: NavigationItem[];
  groups: NavigationGroup[];
}

// Main navigation items (top-level sections)
const mainNavigation: NavigationItem[] = [
  {
    path: '/dashboard',
    icon: '📊',
    label: 'Dashboard',
    description: 'Vista general y métricas',
    requiresAuth: true
  },
  {
    path: '/chat',
    icon: '💬',
    label: 'Chat',
    description: 'Conversaciones con IA',
    requiresAuth: true
  },
  {
    path: '/meetings',
    icon: '🎥',
    label: 'Reuniones',
    description: 'Gestión de reuniones',
    requiresAuth: true
  }
];

// Grouped navigation items (organized by functionality)
const groupedNavigation: NavigationGroup[] = [
  {
    id: 'project-management',
    label: 'Gestión de Proyectos',
    icon: '📁',
    items: [
      {
        path: '/proyectos',
        icon: '📁',
        label: 'Proyectos',
        description: 'Gestión de proyectos',
        requiresAuth: true
      },
      {
        path: '/procesos',
        icon: '⚙️',
        label: 'Procesos',
        description: 'Plantillas y procesos',
        requiresAuth: true
      },
      {
        path: '/tareas',
        icon: '✅',
        label: 'Tareas',
        description: 'Gestión de tareas',
        requiresAuth: true
      },
      {
        path: '/plantillas',
        icon: '📋',
        label: 'Plantillas',
        description: 'Plantillas de procesos',
        requiresAuth: true
      }
    ]
  },
  {
    id: 'crm',
    label: 'CRM',
    icon: '🏢',
    items: [
      {
        path: '/crm/empresas',
        icon: '🏢',
        label: 'Empresas',
        description: 'Gestión de empresas',
        requiresAuth: true
      },
      {
        path: '/crm/personas',
        icon: '👤',
        label: 'Personas',
        description: 'Gestión de contactos',
        requiresAuth: true
      },
      {
        path: '/crm/leads',
        icon: '🎯',
        label: 'Leads',
        description: 'Gestión de leads',
        requiresAuth: true
      }
    ]
  }
];

// Export the complete navigation configuration
export const navigationConfig: NavigationConfig = {
  main: mainNavigation,
  groups: groupedNavigation
};

// Utility functions for navigation management
export const getAllNavigationItems = (): NavigationItem[] => {
  const allItems: NavigationItem[] = [...navigationConfig.main];
  
  navigationConfig.groups.forEach(group => {
    allItems.push(...group.items);
  });
  
  return allItems;
};

export const findNavigationItem = (path: string): NavigationItem | undefined => {
  return getAllNavigationItems().find(item => item.path === path);
};

export const getNavigationByGroup = (groupId: string): NavigationGroup | undefined => {
  return navigationConfig.groups.find(group => group.id === groupId);
};

// Future: Filter navigation by user permissions
export const getFilteredNavigation = (_userRoles?: string[]): NavigationConfig => {
  // For now, return all navigation
  // Future implementation will filter based on user roles
  return navigationConfig;
};

// Validation helper to check if routes exist (for development)
export const validateNavigationRoutes = (availableRoutes: string[]): {
  valid: NavigationItem[];
  invalid: NavigationItem[];
} => {
  const allItems = getAllNavigationItems();
  const valid: NavigationItem[] = [];
  const invalid: NavigationItem[] = [];
  
  allItems.forEach(item => {
    if (availableRoutes.includes(item.path)) {
      valid.push(item);
    } else {
      invalid.push(item);
    }
  });
  
  return { valid, invalid };
};
