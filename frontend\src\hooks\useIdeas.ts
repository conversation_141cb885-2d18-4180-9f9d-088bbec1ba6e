/**
 * Custom hook for managing Ideas state and API interactions
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { apiClient } from '../lib/api';
import { supabase } from '../services/supabaseClient';
import type {
  Idea,
  IdeaCreate,
  IdeaUpdate,
  IdeasFilterState,
  IdeasGroupBy,
  IdeasSortState,
  UseIdeasReturn,
} from '../types/idea';
import {
  groupIdeasBy,
  sortIdeas,
  filterIdeas,
} from '../types/idea';

interface UseIdeasOptions {
  empresaId?: string;
  autoFetch?: boolean;
  enableRealtime?: boolean;
}

export const useIdeas = ({
  empresaId,
  autoFetch = true,
  enableRealtime = true,
}: UseIdeasOptions = {}): UseIdeasReturn => {
  // State
  const [ideas, setIdeas] = useState<Idea[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Filters and UI state
  const [filters, setFilters] = useState<IdeasFilterState>({});
  const [groupBy, setGroupBy] = useState<IdeasGroupBy>('none');
  const [sortState, setSortState] = useState<IdeasSortState>({
    field: 'created_at',
    direction: 'desc',
  });

  // Fetch ideas from API
  const fetchIdeas = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      if (empresaId) {
        // Fetch ideas for specific empresa
        const response = await apiClient.empresas.getIdeasDetails(empresaId);
        setIdeas(response.ideas || []);
      } else {
        // Fetch all ideas with filters
        const params: Record<string, any> = {};
        
        if (filters.estado && filters.estado !== 'all') {
          params.estado = filters.estado;
        }
        
        if (filters.prioridad && filters.prioridad !== 'all') {
          params.prioridad = filters.prioridad;
        }
        
        if (filters.search) {
          params.search = filters.search;
        }

        const response = await apiClient.ideas.getAll(params);
        setIdeas(response.ideas || []);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error loading ideas';
      setError(errorMessage);
      console.error('Error fetching ideas:', err);
    } finally {
      setLoading(false);
    }
  }, [empresaId, filters]);

  // Create idea
  const createIdea = useCallback(async (ideaData: IdeaCreate) => {
    try {
      setError(null);
      const newIdea = await apiClient.ideas.create(ideaData);

      // Immediately add to local state for instant feedback
      setIdeas(prev => {
        // Check if idea already exists to avoid duplicates
        if (prev.some(idea => idea.id === newIdea.id)) {
          return prev;
        }
        return [newIdea, ...prev];
      });

      return newIdea;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error creating idea';
      setError(errorMessage);
      throw err;
    }
  }, []);

  // Update idea
  const updateIdea = useCallback(async (id: string, updates: IdeaUpdate) => {
    try {
      setError(null);
      const updatedIdea = await apiClient.ideas.update(id, updates);
      
      setIdeas(prev => 
        prev.map(idea => 
          idea.id === id ? updatedIdea : idea
        )
      );
      
      return updatedIdea;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error updating idea';
      setError(errorMessage);
      throw err;
    }
  }, []);

  // Delete idea
  const deleteIdea = useCallback(async (id: string) => {
    try {
      setError(null);
      await apiClient.ideas.delete(id);
      setIdeas(prev => prev.filter(idea => idea.id !== id));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error deleting idea';
      setError(errorMessage);
      throw err;
    }
  }, []);

  // Clear filters
  const clearFilters = useCallback(() => {
    setFilters({});
  }, []);

  // Computed values
  const filteredIdeas = useMemo(() => {
    return filterIdeas(ideas, filters);
  }, [ideas, filters]);

  const sortedIdeas = useMemo(() => {
    return sortIdeas(filteredIdeas, sortState);
  }, [filteredIdeas, sortState]);

  const groupedIdeas = useMemo(() => {
    return groupIdeasBy(sortedIdeas, groupBy);
  }, [sortedIdeas, groupBy]);

  const totalCount = ideas.length;
  const filteredCount = filteredIdeas.length;

  // Real-time subscription
  useEffect(() => {
    if (!enableRealtime) return;

    let subscription: any = null;

    const setupRealtimeSubscription = () => {
      console.log('Setting up Ideas realtime subscription for empresa:', empresaId);

      // Create a unique channel name
      const channelName = empresaId ? `ideas_empresa_${empresaId}` : 'ideas_all';

      // Subscribe to ideas table changes
      subscription = supabase
        .channel(channelName)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'ideas',
            filter: empresaId ? `empresa_relacionada_id=eq.${empresaId}` : undefined,
          },
          (payload) => {
            console.log('Ideas real-time update received:', payload);

            switch (payload.eventType) {
              case 'INSERT':
                if (payload.new) {
                  console.log('New idea inserted:', payload.new);
                  // For INSERT, we need to fetch the full idea with relations
                  fetchIdeas();
                }
                break;

              case 'UPDATE':
                if (payload.new) {
                  console.log('Idea updated:', payload.new);
                  // For UPDATE, we also refetch to get updated relations
                  fetchIdeas();
                }
                break;

              case 'DELETE':
                if (payload.old) {
                  console.log('Idea deleted:', payload.old);
                  const deletedIdea = payload.old as any;
                  setIdeas(prev =>
                    prev.filter(idea => idea.id !== deletedIdea.id)
                  );
                }
                break;
            }
          }
        )
        .subscribe((status) => {
          console.log('Ideas subscription status:', status);
        });
    };

    setupRealtimeSubscription();

    return () => {
      if (subscription) {
        console.log('Cleaning up Ideas realtime subscription');
        supabase.removeChannel(subscription);
      }
    };
  }, [enableRealtime, empresaId, fetchIdeas]);

  // Auto-fetch on mount and when dependencies change
  useEffect(() => {
    if (autoFetch) {
      fetchIdeas();
    }
  }, [autoFetch, fetchIdeas]);

  return {
    // Data
    ideas: sortedIdeas,
    loading,
    error,
    
    // Filters and grouping
    filters,
    groupBy,
    sortState,
    
    // Actions
    setFilters,
    setGroupBy,
    setSortState,
    clearFilters,
    updateIdea,
    createIdea,
    deleteIdea,
    
    // Computed data
    groupedIdeas,
    filteredIdeas: sortedIdeas,
    totalCount,
    filteredCount,
  };
};
