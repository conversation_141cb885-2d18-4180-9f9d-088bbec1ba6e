import { useEffect, useCallback, useRef, useState } from 'react';
import { supabase } from '../services/supabaseClient';
import { RealtimeChannel } from '@supabase/supabase-js';

type RealtimeEvent = 'INSERT' | 'UPDATE' | 'DELETE';

interface RealtimePayload<T = any> {
  eventType: RealtimeEvent;
  new: T;
  old: T;
  schema: string;
  table: string;
}

interface UseRealtimeOptions<T> {
  table: string;
  onInsert?: (payload: T) => void;
  onUpdate?: (payload: { old: T; new: T }) => void;
  onDelete?: (payload: T) => void;
  filter?: string;
}

export const useRealtime = <T = any>({
  table,
  onInsert,
  onUpdate,
  onDelete,
  filter
}: UseRealtimeOptions<T>) => {
  const channelRef = useRef<RealtimeChannel | null>(null);
  const callbacksRef = useRef({ onInsert, onUpdate, onDelete });

  // Update callbacks ref when they change
  useEffect(() => {
    callbacksRef.current = { onInsert, onUpdate, onDelete };
  }, [onInsert, onUpdate, onDelete]);

  const setupRealtimeSubscription = useCallback(() => {
    // Clean up existing subscription
    if (channelRef.current) {
      supabase.removeChannel(channelRef.current);
    }

    // Create new channel with unique name to avoid conflicts
    const channelName = `realtime:${table}:${Date.now()}`;
    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes' as any,
        {
          event: '*',
          schema: 'public',
          table: table,
          filter: filter
        },
        (payload: RealtimePayload<T>) => {
          console.log(`Realtime event on ${table}:`, payload);

          const { onInsert, onUpdate, onDelete } = callbacksRef.current;

          switch (payload.eventType) {
            case 'INSERT':
              if (onInsert) {
                onInsert(payload.new);
              }
              break;
            case 'UPDATE':
              if (onUpdate) {
                onUpdate({ old: payload.old, new: payload.new });
              }
              break;
            case 'DELETE':
              if (onDelete) {
                onDelete(payload.old);
              }
              break;
          }
        }
      )
      .subscribe((status) => {
        console.log(`Realtime subscription status for ${table}:`, status);
      });

    channelRef.current = channel;
  }, [table, filter]); // Remove callbacks from dependencies

  useEffect(() => {
    setupRealtimeSubscription();

    // Cleanup on unmount
    return () => {
      if (channelRef.current) {
        supabase.removeChannel(channelRef.current);
        channelRef.current = null;
      }
    };
  }, [setupRealtimeSubscription]);

  const unsubscribe = useCallback(() => {
    if (channelRef.current) {
      supabase.removeChannel(channelRef.current);
      channelRef.current = null;
    }
  }, []);

  const resubscribe = useCallback(() => {
    setupRealtimeSubscription();
  }, [setupRealtimeSubscription]);

  return {
    unsubscribe,
    resubscribe
  };
};

// Specialized hooks for each entity
export const useProjectsRealtime = (
  onProjectChange?: (project: any) => void,
  onProjectDelete?: (project: any) => void
) => {
  return useRealtime({
    table: 'proyectos',
    onInsert: onProjectChange,
    onUpdate: ({ new: newProject }) => onProjectChange?.(newProject),
    onDelete: onProjectDelete
  });
};

export const useProcessesRealtime = (
  onProcessChange?: (process: any) => void,
  onProcessDelete?: (process: any) => void
) => {
  return useRealtime({
    table: 'procesos',
    onInsert: onProcessChange,
    onUpdate: ({ new: newProcess }) => onProcessChange?.(newProcess),
    onDelete: onProcessDelete
  });
};

export const useTasksRealtime = (
  onTaskChange?: (task: any) => void,
  onTaskDelete?: (task: any) => void,
  projectId?: string
) => {
  const filter = projectId ? `proyecto_id=eq.${projectId}` : undefined;

  return useRealtime({
    table: 'tareas',
    onInsert: onTaskChange,
    onUpdate: ({ new: newTask }) => onTaskChange?.(newTask),
    onDelete: onTaskDelete,
    filter
  });
};

// Multi-table realtime hook for dashboard
export const useDashboardRealtime = (
  onDataChange?: () => void
) => {
  const projectsRealtime = useProjectsRealtime(onDataChange, onDataChange);
  const processesRealtime = useProcessesRealtime(onDataChange, onDataChange);
  const tasksRealtime = useTasksRealtime(onDataChange, onDataChange);

  const unsubscribeAll = useCallback(() => {
    projectsRealtime.unsubscribe();
    processesRealtime.unsubscribe();
    tasksRealtime.unsubscribe();
  }, [projectsRealtime, processesRealtime, tasksRealtime]);

  const resubscribeAll = useCallback(() => {
    projectsRealtime.resubscribe();
    processesRealtime.resubscribe();
    tasksRealtime.resubscribe();
  }, [projectsRealtime, processesRealtime, tasksRealtime]);

  return {
    unsubscribeAll,
    resubscribeAll
  };
};

// Hook for connection status monitoring
export const useRealtimeStatus = () => {
  const [isConnected, setIsConnected] = useState(true);
  const [connectionError, setConnectionError] = useState<string | null>(null);

  useEffect(() => {
    const handleOnline = () => {
      setIsConnected(true);
      setConnectionError(null);
    };

    const handleOffline = () => {
      setIsConnected(false);
      setConnectionError('Connection lost');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Check initial connection status
    setIsConnected(navigator.onLine);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return {
    isConnected,
    connectionError
  };
};
