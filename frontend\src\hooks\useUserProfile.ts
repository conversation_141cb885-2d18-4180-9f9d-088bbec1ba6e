import { useState, useEffect } from 'react';
import { User } from '@supabase/supabase-js';
import { getCurrentUserProfile, UserProfile } from '../services/userService';

interface UseUserProfileReturn {
  userProfile: UserProfile | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

/**
 * Hook to manage user profile data from the usuarios table
 * This provides the complete user information including nombre, rol, etc.
 * which is not available in the Supabase Auth user object
 */
export const useUserProfile = (user: User | null): UseUserProfileReturn => {
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const fetchUserProfile = async () => {
    if (!user) {
      setUserProfile(null);
      setLoading(false);
      setError(null);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const profile = await getCurrentUserProfile();
      
      if (profile) {
        setUserProfile(profile);
        setError(null);
      } else {
        setUserProfile(null);
        setError('No se pudo obtener el perfil del usuario');
      }
    } catch (err) {
      console.error('[useUserProfile] Error fetching user profile:', err);
      setUserProfile(null);
      setError('Error al cargar el perfil del usuario');
    } finally {
      setLoading(false);
    }
  };

  // Fetch user profile when user changes
  useEffect(() => {
    fetchUserProfile();
  }, [user?.id]); // Only depend on user.id to avoid unnecessary refetches

  const refetch = async () => {
    await fetchUserProfile();
  };

  return {
    userProfile,
    loading,
    error,
    refetch,
  };
};
