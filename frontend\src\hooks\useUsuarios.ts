import { useState, useEffect } from 'react';
import { apiClient, Usuario } from '../lib/api';

export const useUsuarios = () => {
  const [usuarios, setUsuarios] = useState<Usuario[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchUsuarios = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.usuarios.getAll();
      setUsuarios(response || []);
    } catch (err: any) {
      console.error('Error fetching usuarios:', err);
      setError(err.message || 'Error al cargar usuarios');
      setUsuarios([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsuarios();
  }, []);

  return {
    usuarios,
    loading,
    error,
    refetch: fetchUsuarios
  };
};
