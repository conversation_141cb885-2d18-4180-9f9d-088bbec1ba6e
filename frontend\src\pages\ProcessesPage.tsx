import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useProcesses } from '../hooks/useProcesses';
import { useProcessesRealtime } from '../hooks/useRealtime';
import {
  ProcesoFilters,
  TIPOS_PROCESO,
  TIPO_COLORS,
  VALOR_NEGOCIO_COLORS
} from '../types/proceso';
import { 
  Plus, 
  Search, 
  Filter, 
  Workflow, 
  Building,
  User,
  Clock,
  AlertTriangle,
  RefreshCw,
  ExternalLink
} from 'lucide-react';

const ProcessesPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<ProcesoFilters>({});
  const [showFilters, setShowFilters] = useState(false);

  const {
    processes,
    loading,
    error,
    total,
    currentPage,
    pageSize,
    fetchProcesses,
    setError
  } = useProcesses();

  // Setup realtime updates
  useProcessesRealtime(
    () => fetchProcesses(currentPage, { ...filters, search: searchTerm }),
    () => fetchProcesses(currentPage, { ...filters, search: searchTerm })
  );

  // Apply filters when they change
  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      fetchProcesses(1, { ...filters, search: searchTerm });
    }, 300);

    return () => clearTimeout(delayedSearch);
  }, [searchTerm, filters, fetchProcesses]);

  const handleCreateProcess = () => {
    navigate('/procesos/nuevo');
  };

  const handleProcessClick = (processId: string) => {
    navigate(`/procesos/${processId}`);
  };

  const handlePageChange = (page: number) => {
    fetchProcesses(page, { ...filters, search: searchTerm });
  };

  const clearFilters = () => {
    setFilters({});
    setSearchTerm('');
  };

  const formatTime = (minutes?: number) => {
    if (!minutes) return '-';
    if (minutes < 60) return `${minutes}min`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}min` : `${hours}h`;
  };

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Error al cargar procesos
          </h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => {
              setError(null);
              fetchProcesses(1, { ...filters, search: searchTerm });
            }}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Reintentar
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Procesos</h1>
            <p className="text-gray-600 mt-1">
              Gestiona y optimiza todos tus procesos de negocio
            </p>
          </div>
          
          <button
            onClick={handleCreateProcess}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="h-4 w-4 mr-2" />
            Nuevo Proceso
          </button>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Search */}
            <div className="flex-1 max-w-lg">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Buscar procesos..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Filters Toggle */}
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`inline-flex items-center px-3 py-2 border rounded-lg transition-colors ${
                  showFilters || Object.keys(filters).length > 0
                    ? 'border-blue-300 bg-blue-50 text-blue-700'
                    : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                <Filter className="h-4 w-4 mr-2" />
                Filtros
                {Object.keys(filters).length > 0 && (
                  <span className="ml-2 bg-blue-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                    {Object.keys(filters).length}
                  </span>
                )}
              </button>
            </div>
          </div>

          {/* Filters Panel */}
          {showFilters && (
            <div className="mt-6 pt-6 border-t border-gray-200">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tipo
                  </label>
                  <select
                    value={filters.tipo_proceso || ''}
                    onChange={(e) => setFilters({ ...filters, tipo_proceso: e.target.value as any || undefined })}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Todos los tipos</option>
                    {TIPOS_PROCESO.map((tipo) => (
                      <option key={tipo} value={tipo}>
                        {tipo}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Estado filter removed - procesos table doesn't have estado column */}

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Cuello de botella
                  </label>
                  <select
                    value={filters.es_cuello_botella?.toString() || ''}
                    onChange={(e) => setFilters({ 
                      ...filters, 
                      es_cuello_botella: e.target.value === '' ? undefined : e.target.value === 'true'
                    })}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Todos</option>
                    <option value="true">Solo cuellos de botella</option>
                    <option value="false">Sin cuellos de botella</option>
                  </select>
                </div>

                <div className="flex items-end">
                  <button
                    onClick={clearFilters}
                    className="w-full px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Limpiar filtros
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Results Summary */}
        <div className="flex items-center justify-between mb-6">
          <p className="text-sm text-gray-600">
            Mostrando {processes.length} de {total} procesos
          </p>
          
          {loading && (
            <div className="flex items-center text-sm text-gray-600">
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              Cargando...
            </div>
          )}
        </div>

        {/* Processes Grid */}
        {processes.length === 0 ? (
          <div className="text-center py-12 bg-white rounded-lg border border-gray-200">
            <Workflow className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No se encontraron procesos
            </h3>
            <p className="text-gray-600 mb-4">
              {searchTerm || Object.keys(filters).length > 0
                ? 'Intenta ajustar los filtros de búsqueda.'
                : 'Crea tu primer proceso para comenzar.'}
            </p>
            {!searchTerm && Object.keys(filters).length === 0 && (
              <button
                onClick={handleCreateProcess}
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                <Plus className="h-4 w-4 mr-2" />
                Crear Proceso
              </button>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {processes.map((process) => (
              <div
                key={process.id}
                onClick={() => handleProcessClick(process.id)}
                className={`
                  bg-white rounded-lg border shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer
                  ${process.es_cuello_botella ? 'border-red-200 bg-red-50' : 'border-gray-200 hover:border-gray-300'}
                `}
              >
                <div className="p-6">
                  {/* Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <div className="flex items-center mb-2">
                        <h3 className="text-lg font-semibold text-gray-900 mr-2 line-clamp-1">
                          {process.nombre}
                        </h3>
                        {process.es_cuello_botella && (
                          <AlertTriangle className="h-5 w-5 text-red-500 flex-shrink-0" />
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-2 mb-3">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${TIPO_COLORS[process.tipo_proceso]}`}>
                          {process.tipo_proceso}
                        </span>
                        {process.es_cuello_botella && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            Cuello de Botella
                          </span>
                        )}
                      </div>
                    </div>
                    
                    <Workflow className="h-6 w-6 text-gray-400 ml-2 flex-shrink-0" />
                  </div>

                  {/* Description */}
                  {process.descripcion && (
                    <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                      {process.descripcion}
                    </p>
                  )}

                  {/* Details */}
                  <div className="space-y-3">
                    {process.empresa && (
                      <div className="flex items-center text-sm text-gray-600">
                        <Building className="h-4 w-4 mr-2 flex-shrink-0" />
                        <span className="truncate">{process.empresa.nombre}</span>
                      </div>
                    )}
                    
                    {process.responsable_usuario && (
                      <div className="flex items-center text-sm text-gray-600">
                        <User className="h-4 w-4 mr-2 flex-shrink-0" />
                        <span className="truncate">{process.responsable_usuario.nombre}</span>
                      </div>
                    )}
                    
                    {process.tiempo_estimado_manual && (
                      <div className="flex items-center text-sm text-gray-600">
                        <Clock className="h-4 w-4 mr-2 flex-shrink-0" />
                        <span>{formatTime(process.tiempo_estimado_manual)}</span>
                      </div>
                    )}

                    {process.proyectos_asociados && process.proyectos_asociados.length > 0 && (
                      <div className="flex items-center text-sm text-gray-600">
                        <ExternalLink className="h-4 w-4 mr-2 flex-shrink-0" />
                        <span>{process.proyectos_asociados.length} proyecto(s) asociado(s)</span>
                      </div>
                    )}
                  </div>

                  {/* Value and Automation Info */}
                  <div className="mt-4 pt-4 border-t border-gray-200">
                    <div className="flex items-center justify-between">
                      {process.valor_negocio && (
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${VALOR_NEGOCIO_COLORS[process.valor_negocio] || 'bg-gray-100 text-gray-800'}`}>
                          Valor: {process.valor_negocio}
                        </span>
                      )}
                      
                      {process.es_manual !== undefined && (
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          process.es_manual ? 'bg-orange-100 text-orange-800' : 'bg-green-100 text-green-800'
                        }`}>
                          {process.es_manual ? 'Manual' : 'Automatizado'}
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Bottleneck Warning */}
                  {process.es_cuello_botella && (
                    <div className="mt-4 p-3 bg-red-100 rounded-md">
                      <div className="flex items-center">
                        <AlertTriangle className="h-4 w-4 text-red-600 mr-2" />
                        <span className="text-sm font-medium text-red-800">
                          Cuello de botella identificado
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Pagination */}
        {total > pageSize && (
          <div className="mt-8 flex items-center justify-between">
            <div className="text-sm text-gray-600">
              Página {currentPage} de {Math.ceil(total / pageSize)}
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="px-3 py-2 border border-gray-300 rounded-lg text-sm text-gray-600 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Anterior
              </button>
              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage >= Math.ceil(total / pageSize)}
                className="px-3 py-2 border border-gray-300 rounded-lg text-sm text-gray-600 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Siguiente
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProcessesPage;
