import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  ArrowLeft,
  Calendar,
  User,
  Building2,
  FolderOpen,
  AlertTriangle,
  CheckCircle,
  Clock,
  Edit,
  Plus,
  Tag,
  Trash2,
  Save,
  RefreshCw,
  Target,
  Layers,
  FileText
} from 'lucide-react';
import { Tarea, ESTADOS_TAREA, PRIORIDADES_TAREA, URGENCIAS_TAREA } from '../types/tarea';
import {
  ESTADO_TAREA_COLORS,
  PRIORIDAD_TAREA_COLORS,
  URGENCIA_COLORS
} from '../types/tarea';
import { useTasksRealtime } from '../hooks/useRealtime';
import { useInlineEditSingle } from '../hooks/useInlineEdit';
import { EditableField } from '../components/UI/EditableField';
import { InlineEditableCell } from '../components/UI/InlineEditableCell';
import { useTasks, useProyectos } from '../hooks/useTasks';

import { apiClient } from '../lib/api';

const TaskDetailsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [task, setTask] = useState<Tarea | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { updateTaskField, deleteTask } = useTasks();
  const { proyectos } = useProyectos();

  // Hook para edición in-place
  const {
    data: editableTask,
    updateField,
    isUpdating,
    errors,
    clearError,
    setData
  } = useInlineEditSingle({
    initialData: task,
    updateFunction: updateTaskField,
    onError: (error) => {
      console.error('Error updating task:', error);
    },
    onSuccess: (id, field, value) => {
      console.log(`Task ${id} field ${String(field)} updated to:`, value);
      // Recargar la tarea para obtener datos actualizados
      if (id) fetchTask(id);
    }
  });

  // Setup realtime updates
  useTasksRealtime(
    () => {
      if (id) fetchTask(id);
    },
    () => {
      if (id) fetchTask(id);
    },
    undefined,
    id
  );

  useEffect(() => {
    if (id) {
      fetchTask(id);
    }
  }, [id]);

  // Los datos se actualizan automáticamente con el nuevo hook

  const fetchTask = async (taskId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.tareas.getById(taskId);
      setTask(response as Tarea);
    } catch (err: any) {
      console.error('Error fetching task:', err);
      setError(err.message || 'Error al cargar la tarea');
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    navigate('/tareas');
  };

  const handleDeleteTask = async () => {
    if (!task) return;

    if (window.confirm(`¿Estás seguro de que quieres eliminar la tarea "${task.titulo}"? Esta acción no se puede deshacer.`)) {
      try {
        await deleteTask(task.id);
        navigate('/tareas');
      } catch (error) {
        console.error('Error deleting task:', error);
        alert('Error al eliminar la tarea. Por favor, inténtalo de nuevo.');
      }
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('es-ES');
  };

  const getDaysText = (days?: number) => {
    if (days === undefined || days === null) return null;
    if (days < 0) return `${Math.abs(days)}d vencida`;
    if (days === 0) return 'Vence hoy';
    if (days === 1) return 'Vence mañana';
    return `${days}d restantes`;
  };

  const getProjectType = (task: Tarea) => {
    if (!task.proyecto_nombre) return null;

    const hasEmpresas = task.empresas_asociadas && task.empresas_asociadas.length > 0;
    return hasEmpresas ? 'Externo' : 'Interno (Aceleralia)';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="h-12 w-12 mx-auto mb-4 animate-spin text-blue-600" />
          <p className="text-gray-600">Cargando tarea...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Error al cargar la tarea</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={handleBack}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Volver a Tareas
          </button>
        </div>
      </div>
    );
  }

  if (!task || !editableTask) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Target className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Tarea no encontrada</h2>
          <button
            onClick={handleBack}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Volver a Tareas
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={handleBack}
            className="group flex items-center text-gray-600 hover:text-indigo-600 mb-6 transition-all duration-200 hover:translate-x-1"
          >
            <ArrowLeft className="h-5 w-5 mr-2 group-hover:scale-110 transition-transform" />
            <span className="font-medium">Volver a Tareas</span>
          </button>

          {/* Task Header Card - Enhanced Design */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-white/20 shadow-xl shadow-indigo-100/50 p-8">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <InlineEditableCell
                  value={editableTask.titulo}
                  type="text"
                  onSave={(value) => updateField(editableTask.id, 'titulo', value)}
                  className="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-6 leading-tight"
                  placeholder="Título de la tarea"
                  required
                />

                {/* Uniform Grid Layout - All 5 Rows with Same Structure */}
                <div className="mt-4 grid grid-cols-1 gap-4">
                  {/* Row 1: Estado, Prioridad, Urgencia */}
                  <div className="grid grid-cols-3 gap-4 items-center">
                    <div className="flex justify-center">
                      <EditableField
                        label="Estado"
                        value={editableTask.estado}
                        type="select"
                        options={ESTADOS_TAREA.map(estado => ({ value: estado, label: estado }))}
                        onSave={(value) => updateField(editableTask.id, 'estado', value)}
                        renderValue={(value) => (
                          <div className="group relative">
                            <span className={`inline-flex items-center px-4 py-2 rounded-xl text-sm font-semibold shadow-lg transition-all duration-200 hover:scale-105 ${ESTADO_TAREA_COLORS[value]}`}>
                              <div className="w-2 h-2 rounded-full bg-current mr-2 animate-pulse"></div>
                              {value}
                            </span>
                          </div>
                        )}
                      />
                    </div>
                    <div className="flex justify-center">
                      <EditableField
                        label="Prioridad"
                        value={editableTask.prioridad}
                        type="select"
                        options={PRIORIDADES_TAREA.map(prioridad => ({ value: prioridad, label: prioridad }))}
                        onSave={(value) => updateField(editableTask.id, 'prioridad', value)}
                        renderValue={(value) => (
                          <div className="group relative">
                            <span className={`inline-flex items-center px-4 py-2 rounded-xl text-sm font-semibold shadow-lg transition-all duration-200 hover:scale-105 ${PRIORIDAD_TAREA_COLORS[value]}`}>
                              <Target className="h-3 w-3 mr-2" />
                              {value}
                            </span>
                          </div>
                        )}
                      />
                    </div>
                    <div className="flex justify-center">
                      <EditableField
                        label="Urgencia"
                        value={editableTask.urgencia}
                        type="select"
                        options={URGENCIAS_TAREA.map(urgencia => ({ value: urgencia, label: urgencia }))}
                        onSave={(value) => updateField(editableTask.id, 'urgencia', value)}
                        renderValue={(value) => (
                          <div className="group relative">
                            <span className={`inline-flex items-center px-4 py-2 rounded-xl text-sm font-semibold shadow-lg transition-all duration-200 hover:scale-105 ${URGENCIA_COLORS[value]}`}>
                              <Clock className="h-3 w-3 mr-2" />
                              {value}
                            </span>
                          </div>
                        )}
                      />
                    </div>
                  </div>

                  {/* Row 2: Fechas */}
                  <div className="grid grid-cols-3 gap-4 items-center">
                    <div className="flex justify-center">
                      <EditableField
                        label="Fecha de vencimiento"
                        value={editableTask.fecha_vencimiento || ''}
                        type="date"
                        onSave={(value) => updateField(editableTask.id, 'fecha_vencimiento', value)}
                        renderValue={(value) => {
                          if (!value) return (
                            <div className="group relative">
                              <span className="inline-flex items-center px-3 py-1.5 rounded-lg text-xs font-medium shadow-md transition-all duration-200 hover:scale-105 bg-gray-100 text-gray-600">
                                <Calendar className="h-3 w-3 mr-1.5" />
                                Sin vencimiento
                              </span>
                            </div>
                          );

                          const isOverdue = editableTask.es_vencida;
                          const isNearDue = editableTask.dias_vencimiento !== null && editableTask.dias_vencimiento <= 3 && !isOverdue;

                          return (
                            <div className="group relative">
                              <span className={`inline-flex items-center px-3 py-1.5 rounded-lg text-xs font-medium shadow-md transition-all duration-200 hover:scale-105 ${
                                isOverdue
                                  ? 'bg-red-100 text-red-700'
                                  : isNearDue
                                    ? 'bg-yellow-100 text-yellow-700'
                                    : 'bg-blue-100 text-blue-700'
                              }`}>
                                <Calendar className="h-3 w-3 mr-1.5" />
                                Vence: {formatDate(value)}
                                {editableTask.dias_vencimiento !== null && (
                                  <span className="ml-1.5 text-xs opacity-75">
                                    ({getDaysText(editableTask.dias_vencimiento)})
                                  </span>
                                )}
                              </span>
                            </div>
                          );
                        }}
                      />
                    </div>
                    <div className="flex justify-center">
                      <EditableField
                        label="Fecha de inicio"
                        value={editableTask.fecha_inicio || ''}
                        type="date"
                        onSave={(value) => updateField(editableTask.id, 'fecha_inicio', value)}
                        renderValue={(value) => {
                          if (!value) return (
                            <div className="group relative">
                              <span className="inline-flex items-center px-3 py-1.5 rounded-lg text-xs font-medium shadow-md transition-all duration-200 hover:scale-105 bg-gray-100 text-gray-600">
                                <Calendar className="h-3 w-3 mr-1.5" />
                                Sin inicio
                              </span>
                            </div>
                          );

                          return (
                            <div className="group relative">
                              <span className="inline-flex items-center px-3 py-1.5 rounded-lg text-xs font-medium shadow-md transition-all duration-200 hover:scale-105 bg-green-100 text-green-700">
                                <Calendar className="h-3 w-3 mr-1.5" />
                                Inicio: {formatDate(value)}
                              </span>
                            </div>
                          );
                        }}
                      />
                    </div>
                    <div className="flex justify-center">
                      <EditableField
                        label="Fecha de completado"
                        value={editableTask.fecha_completado || ''}
                        type="date"
                        onSave={(value) => updateField(editableTask.id, 'fecha_completado', value)}
                        renderValue={(value) => {
                          if (!value) return (
                            <div className="group relative">
                              <span className="inline-flex items-center px-3 py-1.5 rounded-lg text-xs font-medium shadow-md transition-all duration-200 hover:scale-105 bg-gray-100 text-gray-600">
                                <Calendar className="h-3 w-3 mr-1.5" />
                                Sin completar
                              </span>
                            </div>
                          );

                          return (
                            <div className="group relative">
                              <span className="inline-flex items-center px-3 py-1.5 rounded-lg text-xs font-medium shadow-md transition-all duration-200 hover:scale-105 bg-purple-100 text-purple-700">
                                <Calendar className="h-3 w-3 mr-1.5" />
                                Completado: {formatDate(value)}
                              </span>
                            </div>
                          );
                        }}
                      />
                    </div>
                  </div>

                  {/* Row 3: Proyecto, Empresas, Etiquetas */}
                  <div className="grid grid-cols-3 gap-4 items-start">
                    <div className="flex flex-col items-center space-y-2">
                      <span className="text-sm font-medium text-gray-600">Proyecto asignado:</span>
                      <EditableField
                        label="Proyecto asignado"
                        value={editableTask.proyecto_id || ''}
                        type="select"
                        options={[
                          { value: '', label: 'Sin proyecto' },
                          ...proyectos.map(proyecto => ({ value: proyecto.id, label: proyecto.nombre }))
                        ]}
                        onSave={(value) => updateField(editableTask.id, 'proyecto_id', value)}
                        renderValue={(value) => {
                          if (!value) return (
                            <div className="group relative">
                              <span className="inline-flex items-center px-3 py-1.5 rounded-lg text-xs font-medium shadow-md transition-all duration-200 hover:scale-105 bg-gray-100 text-gray-600">
                                <FolderOpen className="h-3 w-3 mr-1.5" />
                                Sin proyecto
                              </span>
                            </div>
                          );
                          const proyecto = proyectos.find(p => p.id === value);
                          const projectType = getProjectType(editableTask);
                          return (
                            <div className="group relative">
                              <span className="inline-flex items-center px-3 py-1.5 rounded-lg text-xs font-medium shadow-md transition-all duration-200 hover:scale-105 bg-blue-100 text-blue-700">
                                <FolderOpen className="h-3 w-3 mr-1.5" />
                                {proyecto?.nombre || 'Proyecto no encontrado'}
                                {projectType && (
                                  <span className="ml-1.5 text-xs opacity-75">
                                    ({projectType})
                                  </span>
                                )}
                              </span>
                            </div>
                          );
                        }}
                      />
                    </div>

                    <div className="flex flex-col items-center space-y-2">
                      <span className="text-sm font-medium text-gray-600">Empresas asociadas:</span>
                      <div className="flex flex-wrap gap-2 justify-center">
                        {editableTask.empresas_asociadas && editableTask.empresas_asociadas.length > 0 ? (
                          editableTask.empresas_asociadas.map((empresa, index) => (
                            <div key={empresa.id || index} className="group relative">
                              <span
                                className="inline-flex items-center px-3 py-1.5 rounded-lg text-xs font-medium shadow-md transition-all duration-200 hover:scale-105 bg-indigo-100 text-indigo-700 cursor-pointer"
                                onClick={() => navigate(`/crm/empresas/${empresa.id}`)}
                              >
                                <Building2 className="h-3 w-3 mr-1.5" />
                                {empresa.nombre}
                              </span>
                            </div>
                          ))
                        ) : (
                          <span className="text-sm text-gray-500 italic">Sin empresas asociadas</span>
                        )}
                      </div>
                    </div>

                    <div className="flex flex-col items-center space-y-2">
                      <span className="text-sm font-medium text-gray-600">Etiquetas:</span>
                      <div className="flex flex-wrap gap-2 justify-center">
                        {editableTask.etiquetas && editableTask.etiquetas.length > 0 ? (
                          editableTask.etiquetas.map((etiqueta, index) => (
                            <div key={etiqueta.id || index} className="group relative">
                              <span
                                className="inline-flex items-center px-3 py-1.5 rounded-lg text-xs font-medium shadow-md transition-all duration-200 hover:scale-105"
                                style={{
                                  backgroundColor: etiqueta.color ? `${etiqueta.color}30` : '#f3f4f6',
                                  color: etiqueta.color || '#374151',
                                  border: `1px solid ${etiqueta.color || '#d1d5db'}`
                                }}
                              >
                                <Tag className="h-3 w-3 mr-1.5" />
                                {etiqueta.nombre}
                              </span>
                            </div>
                          ))
                        ) : (
                          <span className="text-sm text-gray-500 italic">Sin etiquetas asignadas</span>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Vencida indicator */}
                  {editableTask.es_vencida && (
                    <div className="grid grid-cols-3 gap-4 items-center">
                      <div></div>
                      <div className="flex justify-center animate-pulse">
                        <span className="flex items-center bg-red-100 text-red-700 px-4 py-2 rounded-xl text-sm font-semibold shadow-lg">
                          <AlertTriangle className="h-4 w-4 mr-2 animate-bounce" />
                          Vencida
                        </span>
                      </div>
                      <div></div>
                    </div>
                  )}
                </div>

                {/* Progress indicator if task has subtasks */}
                {task.subtareas && task.subtareas.length > 0 && (
                  <div className="mb-4">
                    <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                      <span>Progreso de subtareas</span>
                      <span>{task.subtareas.filter(st => st.estado === 'Completada').length}/{task.subtareas.length}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-green-400 to-green-600 h-2 rounded-full transition-all duration-500"
                        style={{
                          width: `${(task.subtareas.filter(st => st.estado === 'Completada').length / task.subtareas.length) * 100}%`
                        }}
                      ></div>
                    </div>
                  </div>
                )}
              </div>

              <div className="flex flex-col items-end space-y-4 ml-8">
                {isUpdating && (
                  <div className="flex items-center bg-blue-50 text-blue-700 px-4 py-2 rounded-xl shadow-lg">
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    <span className="text-sm font-medium">Guardando...</span>
                  </div>
                )}

                <div className="flex flex-col space-y-3">
                  <button
                    onClick={handleDeleteTask}
                    className="group flex items-center px-6 py-3 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-xl hover:from-red-600 hover:to-red-700 transition-all duration-200 shadow-lg hover:shadow-xl hover:scale-105"
                  >
                    <Trash2 className="h-4 w-4 mr-2 group-hover:animate-bounce" />
                    <span className="font-medium">Eliminar</span>
                  </button>

                  <button
                    onClick={() => navigate(`/tareas/nueva?parent=${task.id}`)}
                    className="group flex items-center px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-xl hover:from-green-600 hover:to-emerald-700 transition-all duration-200 shadow-lg hover:shadow-xl hover:scale-105"
                  >
                    <Plus className="h-4 w-4 mr-2 group-hover:rotate-90 transition-transform duration-200" />
                    <span className="font-medium">Nueva subtarea</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content - Reorganized Layout */}
        <div className="space-y-8">
          {/* Description Section - First for Quick Access */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-white/20 shadow-xl shadow-blue-100/30 p-8">
            <div className="flex items-center mb-6">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mr-4">
                <FileText className="h-5 w-5 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900">Descripción</h2>
            </div>

            <EditableField
              label=""
              value={editableTask.descripcion || ''}
              type="textarea"
              onSave={(value) => updateField(editableTask.id, 'descripcion', value)}
              renderValue={(value) => (
                <div className="prose prose-gray max-w-none">
                  {value ? (
                    <div className="whitespace-pre-wrap text-gray-700 leading-relaxed">
                      {value}
                    </div>
                  ) : (
                    <span className="text-gray-500 italic">Sin descripción</span>
                  )}
                </div>
              )}
            />
          </div>










          {/* Main Content Section */}
          <div className="space-y-8">
              {/* Additional Info */}
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-white/20 shadow-xl shadow-purple-100/30 p-8">
                <div className="flex items-center mb-6">
                  <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl flex items-center justify-center mr-4">
                    <Plus className="h-5 w-5 text-white" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900">Información Adicional</h2>
                </div>
                <div className="bg-gray-50/50 rounded-xl p-6 border border-gray-100">
                  <EditableField
                    label=""
                    value={editableTask.info_adicional || ''}
                    type="textarea"
                    onSave={(value) => updateField(editableTask.id, 'info_adicional', value)}
                    placeholder="Información adicional, notas, comentarios..."
                    className="text-gray-700 leading-relaxed"
                  />
                </div>
              </div>
            </div>

            {/* Subtasks Section */}
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-white/20 shadow-xl shadow-green-100/30 p-8">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl flex items-center justify-center mr-4">
                    <Layers className="h-5 w-5 text-white" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900">
                    Subtareas {task.subtareas && task.subtareas.length > 0 && `(${task.subtareas.length})`}
                  </h2>
                </div>
                <button
                  onClick={() => navigate(`/tareas/nueva?parent=${task.id}`)}
                  className="group flex items-center px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-xl hover:from-green-600 hover:to-emerald-700 transition-all duration-200 shadow-lg hover:shadow-xl hover:scale-105"
                >
                  <Plus className="h-4 w-4 mr-2 group-hover:rotate-90 transition-transform duration-200" />
                  <span className="font-medium">Agregar subtarea</span>
                </button>
              </div>

              {task.subtareas && task.subtareas.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
                  {task.subtareas.map((subtask, index) => (
                    <div
                      key={subtask.id}
                      className="group relative bg-gradient-to-r from-white to-gray-50 rounded-xl border border-gray-200 p-5 hover:shadow-lg transition-all duration-200 hover:scale-[1.02]"
                    >
                      <div className="space-y-3">
                        <div className="flex items-start justify-between">
                          <div className="flex items-start space-x-3">
                            <div className="relative mt-1">
                              <div className={`w-5 h-5 rounded-full border-2 transition-all duration-200 ${
                                subtask.estado === 'Completada'
                                  ? 'bg-green-500 border-green-500'
                                  : 'bg-white border-gray-300 group-hover:border-green-400'
                              }`}>
                                {subtask.estado === 'Completada' && (
                                  <CheckCircle className="w-3 h-3 text-white absolute top-0.5 left-0.5" />
                                )}
                              </div>
                              <div className="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full text-xs text-white flex items-center justify-center font-bold">
                                {index + 1}
                              </div>
                            </div>
                            <div className="flex-1 min-w-0">
                              <span className={`font-semibold text-sm transition-all duration-200 block ${
                                subtask.estado === 'Completada'
                                  ? 'text-gray-500 line-through'
                                  : 'text-gray-900 group-hover:text-indigo-600'
                              }`}>
                                {subtask.titulo}
                              </span>
                              {subtask.descripcion && (
                                <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                                  {subtask.descripcion}
                                </p>
                              )}
                            </div>
                          </div>
                          <button
                            onClick={() => navigate(`/tareas/${subtask.id}`)}
                            className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-1 text-indigo-600 hover:bg-indigo-50 rounded-lg"
                          >
                            <ArrowLeft className="h-3 w-3 rotate-180" />
                          </button>
                        </div>

                        <div className="flex flex-wrap gap-2">
                          <span className={`px-2 py-1 rounded-lg text-xs font-semibold shadow-sm ${ESTADO_TAREA_COLORS[subtask.estado]}`}>
                            {subtask.estado}
                          </span>
                          <span className={`px-2 py-1 rounded-lg text-xs font-semibold shadow-sm ${PRIORIDAD_TAREA_COLORS[subtask.prioridad]}`}>
                            {subtask.prioridad}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Layers className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-600 mb-2">No hay subtareas para esta tarea</h3>
                  <p className="text-gray-500 mb-6">Las subtareas te ayudan a dividir el trabajo en partes más pequeñas y manejables.</p>
                  <button
                    onClick={() => navigate(`/tareas/nueva?parent=${task.id}`)}
                    className="group inline-flex items-center px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-xl hover:from-green-600 hover:to-emerald-700 transition-all duration-200 shadow-lg hover:shadow-xl hover:scale-105"
                  >
                    <Plus className="h-4 w-4 mr-2 group-hover:rotate-90 transition-transform duration-200" />
                    <span className="font-medium">Crear primera subtarea</span>
                  </button>
                </div>
              )}
            </div>


        </div>
      </div>
    </div>
  );
};

export default TaskDetailsPage;
