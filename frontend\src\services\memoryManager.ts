// Basic memory management service for chat application
// Simplified version to avoid import issues

// Type declaration for WeakRef (for older TypeScript versions)
declare global {
  interface WeakRef<T extends object> {
    deref(): T | undefined;
  }
  interface WeakRefConstructor {
    new <T extends object>(target: T): WeakRef<T>;
  }
  var WeakRef: WeakRefConstructor | undefined;
}

// Basic configuration constants
const MEMORY_CONFIG = {
  CACHE_CLEANUP_INTERVAL: 300000, // 5 minutes
  MEMORY_USAGE_THRESHOLD: 50 * 1024 * 1024, // 50MB
  ENABLE_PERFORMANCE_TRACKING: true,
  ENABLE_CONSOLE_LOGS: process.env.NODE_ENV === 'development',
};

interface MemoryStats {
  usedJSHeapSize?: number;
  totalJSHeapSize?: number;
  jsHeapSizeLimit?: number;
}

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  accessCount: number;
  lastAccessed: number;
}

class MemoryManager {
  private static instance: MemoryManager;
  private caches = new Map<string, Map<string, CacheEntry<any>>>();
  private cleanupIntervals = new Map<string, NodeJS.Timeout>();
  private memoryWarningThreshold = MEMORY_CONFIG.MEMORY_USAGE_THRESHOLD;

  private constructor() {
    this.startGlobalCleanup();
    this.setupMemoryMonitoring();
  }

  static getInstance(): MemoryManager {
    if (!MemoryManager.instance) {
      MemoryManager.instance = new MemoryManager();
    }
    return MemoryManager.instance;
  }

  // Create a new cache with automatic cleanup
  createCache<T>(
    name: string, 
    maxSize: number = 100, 
    ttl: number = 300000 // 5 minutes default
  ): {
    set: (key: string, value: T) => void;
    get: (key: string) => T | undefined;
    delete: (key: string) => boolean;
    clear: () => void;
    size: () => number;
  } {
    const cache = new Map<string, CacheEntry<T>>();
    this.caches.set(name, cache);

    // Setup automatic cleanup for this cache
    const cleanupInterval = setInterval(() => {
      this.cleanupCache(name, maxSize, ttl);
    }, MEMORY_CONFIG.CACHE_CLEANUP_INTERVAL);

    this.cleanupIntervals.set(name, cleanupInterval);

    return {
      set: (key: string, value: T) => {
        const now = Date.now();
        cache.set(key, {
          data: value,
          timestamp: now,
          accessCount: 1,
          lastAccessed: now,
        });

        // Immediate cleanup if cache is too large
        if (cache.size > maxSize * 1.2) {
          this.cleanupCache(name, maxSize, ttl);
        }
      },

      get: (key: string) => {
        const entry = cache.get(key);
        if (!entry) return undefined;

        // Check if entry has expired
        if (Date.now() - entry.timestamp > ttl) {
          cache.delete(key);
          return undefined;
        }

        // Update access statistics
        entry.accessCount++;
        entry.lastAccessed = Date.now();
        return entry.data;
      },

      delete: (key: string) => cache.delete(key),
      clear: () => cache.clear(),
      size: () => cache.size,
    };
  }

  // Cleanup a specific cache
  private cleanupCache(name: string, maxSize: number, ttl: number) {
    const cache = this.caches.get(name);
    if (!cache) return;

    const now = Date.now();
    const entries = Array.from(cache.entries());

    // Remove expired entries
    const validEntries = entries.filter(([key, entry]) => {
      if (now - entry.timestamp > ttl) {
        cache.delete(key);
        return false;
      }
      return true;
    });

    // If still over limit, remove least recently used entries
    if (validEntries.length > maxSize) {
      validEntries
        .sort((a, b) => a[1].lastAccessed - b[1].lastAccessed)
        .slice(0, validEntries.length - maxSize)
        .forEach(([key]) => cache.delete(key));
    }

    if (MEMORY_CONFIG.ENABLE_CONSOLE_LOGS) {
      console.log(`Cleaned up cache "${name}": ${entries.length} -> ${cache.size} entries`);
    }
  }

  // Force cleanup of all caches
  forceCleanup() {
    this.caches.forEach((cache, name) => {
      const maxSize = Math.floor(cache.size * 0.7); // Keep 70% of entries
      this.cleanupCache(name, maxSize, 0); // TTL 0 means remove old entries aggressively
    });

    // Force garbage collection if available
    if ('gc' in window && typeof (window as any).gc === 'function') {
      (window as any).gc();
    }
  }

  // Get memory statistics
  getMemoryStats(): MemoryStats {
    const stats: MemoryStats = {};
    
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      stats.usedJSHeapSize = memory.usedJSHeapSize;
      stats.totalJSHeapSize = memory.totalJSHeapSize;
      stats.jsHeapSizeLimit = memory.jsHeapSizeLimit;
    }

    return stats;
  }

  // Check if memory usage is high
  isMemoryUsageHigh(): boolean {
    const stats = this.getMemoryStats();
    return stats.usedJSHeapSize ? stats.usedJSHeapSize > this.memoryWarningThreshold : false;
  }

  // Get cache statistics
  getCacheStats() {
    const stats: Record<string, any> = {};
    
    this.caches.forEach((cache, name) => {
      const entries = Array.from(cache.values());
      stats[name] = {
        size: cache.size,
        totalAccessCount: entries.reduce((sum, entry) => sum + entry.accessCount, 0),
        averageAge: entries.length > 0 
          ? (Date.now() - entries.reduce((sum, entry) => sum + entry.timestamp, 0) / entries.length) / 1000
          : 0,
      };
    });

    return stats;
  }

  // Setup memory monitoring
  private setupMemoryMonitoring() {
    if (!MEMORY_CONFIG.ENABLE_PERFORMANCE_TRACKING) return;

    setInterval(() => {
      const stats = this.getMemoryStats();
      
      if (this.isMemoryUsageHigh()) {
        console.warn('High memory usage detected:', stats);
        this.forceCleanup();
      }

      if (MEMORY_CONFIG.ENABLE_CONSOLE_LOGS) {
        console.log('Memory stats:', stats);
        console.log('Cache stats:', this.getCacheStats());
      }
    }, 30000); // Check every 30 seconds
  }

  // Global cleanup interval
  private startGlobalCleanup() {
    setInterval(() => {
      this.forceCleanup();
    }, MEMORY_CONFIG.CACHE_CLEANUP_INTERVAL * 2); // Every 10 minutes
  }

  // Cleanup on page unload
  setupUnloadCleanup() {
    const cleanup = () => {
      this.cleanupIntervals.forEach(interval => clearInterval(interval));
      this.caches.clear();
    };

    window.addEventListener('beforeunload', cleanup);
    window.addEventListener('pagehide', cleanup);

    return cleanup;
  }

  // Destroy the memory manager
  destroy() {
    this.cleanupIntervals.forEach(interval => clearInterval(interval));
    this.cleanupIntervals.clear();
    this.caches.clear();
  }
}

// Export singleton instance
export const memoryManager = MemoryManager.getInstance();

// Utility functions for common memory management tasks
export const memoryUtils = {
  // Create a debounced function to prevent excessive calls
  debounce: <T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): ((...args: Parameters<T>) => void) => {
    let timeout: NodeJS.Timeout;
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
    };
  },

  // Create a throttled function to limit call frequency
  throttle: <T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): ((...args: Parameters<T>) => void) => {
    let inThrottle: boolean;
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  },

  // Weak reference utility for preventing memory leaks (with fallback for older browsers)
  createWeakRef: <T extends object>(obj: T): { deref: () => T | undefined } => {
    if (typeof WeakRef !== 'undefined') {
      return new WeakRef(obj);
    }
    // Fallback for browsers that don't support WeakRef
    return {
      deref: () => obj
    };
  },

  // Check if an object is still alive in a weak reference
  isWeakRefAlive: <T extends object>(ref: { deref: () => T | undefined }): boolean => {
    return ref.deref() !== undefined;
  },
};

export default memoryManager;
