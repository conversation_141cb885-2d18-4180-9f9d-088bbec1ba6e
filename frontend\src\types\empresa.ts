/**
 * TypeScript types for Empresa (Company) entities
 * Based on backend Pydantic models and database schema
 */

export type TipoRelacion = 'Cliente' | 'Colaborador' | 'Otro';

// Base interface for common empresa fields
export interface EmpresaBase {
  nombre: string;
  nif_cif?: string | null;
  sector?: string | null;
  descripcion?: string | null;
  logo_url?: string | null;
  direccion?: string | null;
  direccion_fiscal?: string | null;
  telefono?: string | null;
  email_principal?: string | null;
  website?: string | null;
  tipo_empresa?: string | null;
  tipo_relacion?: TipoRelacion | null;
  activo: boolean;
  info_adicional?: string | null;
}

// Interface for creating a new empresa
export interface EmpresaCreate extends EmpresaBase {}

// Interface for updating an existing empresa
export interface EmpresaUpdate extends Partial<EmpresaBase> {}

// Complete empresa interface (from API responses)
export interface Empresa extends EmpresaBase {
  id: string;
  fecha_alta?: string | null; // ISO date string
  created_at: string; // ISO datetime string
  updated_at: string; // ISO datetime string
}

// API response types
export interface EmpresaListResponse {
  data: Empresa[];
  total: number;
  page: number;
  limit: number;
}

// For dropdowns and selectors
export interface EmpresaOption {
  id: string;
  nombre: string;
}

// Form validation schema type (for react-hook-form)
export interface EmpresaFormValues extends EmpresaBase {}

// API parameters for listing empresas
export interface EmpresasListParams {
  search?: string;
  skip?: number;
  limit?: number;
  tipo_relacion?: TipoRelacion;
  activo?: boolean;
}

// Tab types for empresa details view
export type EmpresaDetailTab = 'general' | 'reuniones' | 'hallazgos' | 'procesos_clientes' | 'ideas';

export interface EmpresaDetailTabConfig {
  id: EmpresaDetailTab;
  label: string;
  icon: string;
  component?: React.ComponentType<{ empresaId: string; empresaNombre?: string }>;
}

// Statistics and summary types
export interface EmpresaStats {
  total_empresas: number;
  clientes: number;
  colaboradores: number;
  otros: number;
  activas: number;
  inactivas: number;
}

// Error types
export interface EmpresaError {
  message: string;
  field?: string;
  code?: string;
}

// API response wrapper
export interface EmpresaApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: EmpresaError;
  message?: string;
}

// General Tab Types
export interface DepartmentInfo {
  id: string;
  nombre: string;
  descripcion?: string | null;
}

export interface ProjectInfo {
  id: string;
  nombre: string;
  descripcion?: string | null;
  progreso: number; // 0-100
  estado?: string | null;
  fecha_inicio?: string | null;
  fecha_fin_estimada?: string | null;
}

export interface MeetingStats {
  total_reuniones: number;
  total_entrevistas: number;
  reuniones_vs_entrevistas_ratio: number;
}

export interface ProcessStats {
  total_procesos_clientes: number;
  total_tareas_clientes: number;
}

export interface FindingDistribution {
  tipo: string;
  count: number;
  percentage: number;
}

export interface PeoplePanel {
  total_personas_activas: number;
  total_personas_entrevistadas: number;
  porcentaje_entrevistadas: number;
}

export interface EmpresaGeneralDetails {
  // Basic company info
  empresa: Empresa;

  // Main information section
  departamentos: DepartmentInfo[];
  proyectos_relacionados: ProjectInfo[];
  total_trabajadores_activos: number;

  // Statistics panel
  estadisticas_reuniones: MeetingStats;
  estadisticas_procesos: ProcessStats;
  distribucion_hallazgos: FindingDistribution[];
  panel_personas: PeoplePanel;
}

// Hallazgos Tab Types
export interface HallazgoListItem {
  id: string;
  titulo?: string;
  tipo?: string;
  impacto?: string;
  departamento_nombre?: string;
  persona_nombre?: string;
  estado?: string;
  created_at: string;
}

export interface HallazgoDetail {
  id: string;
  titulo?: string;
  tipo?: string;
  impacto?: string;
  descripcion?: string;
  posible_solucion?: string;
  estado?: string;
  departamento_nombre?: string;
  persona_nombre?: string;
  procesos_relacionados: string[];
  created_at: string;
  updated_at: string;
}

export interface HallazgoTypeStats {
  tipo: string;
  count: number;
  percentage: number;
}

export interface HallazgoFilters {
  personas_disponibles: Array<{ id: string; nombre_completo: string }>;
  departamentos_disponibles: Array<{ id: string; nombre: string }>;
  tipos_disponibles: string[];
  impactos_disponibles: string[];
}

export interface EmpresaHallazgosDetails {
  total_hallazgos: number;
  distribucion_tipos: HallazgoTypeStats[];
  hallazgos: HallazgoListItem[];
  filtros_disponibles: HallazgoFilters;
}

// Hallazgo filter and sort types
export interface HallazgoFilterState {
  persona_id?: string;
  departamento_id?: string;
  tipo?: string;
  impacto?: string;
  search?: string;
}

export type HallazgoSortField = 'titulo' | 'tipo' | 'impacto' | 'created_at';
export type SortDirection = 'asc' | 'desc';

export interface HallazgoSortState {
  field: HallazgoSortField;
  direction: SortDirection;
}

// Reuniones Tab Types
export interface PersonaEstadoEntrevista {
  id: string;
  nombre: string;
  apellidos?: string | null;
  cargo?: string | null;
  entrevistado: boolean;
  total_reuniones_asistidas: number;
}

export interface ReunionListItem {
  id: string;
  titulo?: string | null;
  fecha_reunion?: string | null;
  tipo: string; // "Reunión" or "Entrevista"
  estado_procesamiento?: string | null;
  personas_asistentes: string[]; // Names of attendees from this company
  personas_asistentes_ids: string[]; // IDs for filtering
}

export interface ReunionTimelineItem {
  id: string;
  titulo?: string | null;
  fecha_reunion?: string | null;
  tipo: string; // "Reunión" or "Entrevista"
  resumen_breve?: string | null;
  personas_asistentes: string[];
}

export interface ReunionStats {
  total_reuniones: number;
  total_entrevistas: number;
  total_personas_entrevistadas: number;
  porcentaje_personas_entrevistadas: number;
}

export interface EmpresaReunionesDetails {
  reuniones_list: ReunionListItem[];
  personas_empresa: PersonaEstadoEntrevista[];
  timeline_items: ReunionTimelineItem[];
  estadisticas: ReunionStats;
  total_reuniones: number;
}

// Reuniones filter and sort types
export interface ReunionesFilterState {
  tipo?: 'todas' | 'reuniones' | 'entrevistas';
  persona_id?: string;
  search?: string;
}

export type ReunionSortField = 'titulo' | 'fecha_reunion' | 'tipo';

export interface ReunionSortState {
  field: ReunionSortField;
  direction: SortDirection;
}
