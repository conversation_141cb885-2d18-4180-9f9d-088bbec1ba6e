export type TipoProceso = 'Interno' | 'Externo';

export interface HerramientaUtilizada {
  nombre_herramienta: string;
  uso_principal: string;
}

export interface Proceso {
  id: string;
  nombre: string;
  descripcion?: string;
  tipo_proceso: TipoProceso;
  empresa_id?: string;
  departamento_id?: string;
  persona_id?: string;
  es_repetitivo?: boolean;
  es_cuello_botella?: boolean;
  es_manual?: boolean;
  valor_negocio?: string;
  complejidad_automatizacion?: string;
  prioridad_automatizacion?: string;
  tiempo_estimado_manual?: number;
  frecuencia?: string;
  proceso_plantilla_origen_id?: string;
  herramientas_utilizadas?: HerramientaUtilizada[] | Record<string, any>;
  info_adicional?: string;
  created_at: string;
  updated_at: string;
  
  // Related data
  empresa?: {
    id: string;
    nombre: string;
    tipo_relacion: string;
  };
  responsable_usuario?: {
    id: string;
    nombre: string;
    email: string;
  };
  plantilla_origen?: string;
  proyectos_asociados?: Array<{
    id: string;
    nombre: string;
    estado: string;
  }>;
  total_tareas?: number;
}

export interface ProcesoCreate {
  nombre: string;
  descripcion?: string;
  tipo_proceso: TipoProceso;
  empresa_id?: string;
  departamento_id?: string;
  persona_id?: string;
  es_repetitivo?: boolean;
  es_cuello_botella?: boolean;
  es_manual?: boolean;
  valor_negocio?: string;
  complejidad_automatizacion?: string;
  prioridad_automatizacion?: string;
  tiempo_estimado_manual?: number;
  frecuencia?: string;
  proceso_plantilla_origen_id?: string;
  herramientas_utilizadas?: HerramientaUtilizada[] | Record<string, any>;
  info_adicional?: string;
}

export interface ProcesoUpdate {
  nombre?: string;
  descripcion?: string;
  tipo_proceso?: TipoProceso;
  empresa_id?: string;
  departamento_id?: string;
  persona_id?: string;
  es_repetitivo?: boolean;
  es_cuello_botella?: boolean;
  es_manual?: boolean;
  valor_negocio?: string;
  complejidad_automatizacion?: string;
  prioridad_automatizacion?: string;
  tiempo_estimado_manual?: number;
  frecuencia?: string;
  proceso_plantilla_origen_id?: string;
  herramientas_utilizadas?: HerramientaUtilizada[] | Record<string, any>;
  info_adicional?: string;
}

export interface ProcesoSummary {
  id: string;
  nombre: string;
  tipo_proceso: TipoProceso;
  empresa_nombre?: string;
  responsable_nombre?: string;
  tiempo_estimado_manual?: number;
  es_cuello_botella: boolean;
}

export interface ProcesoListResponse {
  procesos: Proceso[];
  total: number;
  page: number;
  size: number;
}

export interface ProcesoFilters {
  tipo_proceso?: TipoProceso;
  empresa_id?: string;
  es_cuello_botella?: boolean;
  search?: string;
}

// Constants
export const TIPOS_PROCESO: TipoProceso[] = ['Interno', 'Externo'];

export const VALORES_NEGOCIO = [
  'Bajo',
  'Medio',
  'Alto',
  'Crítico'
] as const;

export const COMPLEJIDAD_AUTOMATIZACION = [
  'Baja',
  'Media',
  'Alta',
  'Muy Alta'
] as const;

export const PRIORIDAD_AUTOMATIZACION = [
  'Baja',
  'Media',
  'Alta',
  'Urgente'
] as const;

// Color mappings for UI
export const TIPO_COLORS: Record<TipoProceso, string> = {
  'Interno': 'bg-blue-100 text-blue-800',
  'Externo': 'bg-green-100 text-green-800'
};

export const VALOR_NEGOCIO_COLORS: Record<string, string> = {
  'Bajo': 'bg-gray-100 text-gray-800',
  'Medio': 'bg-blue-100 text-blue-800',
  'Alto': 'bg-orange-100 text-orange-800',
  'Crítico': 'bg-red-100 text-red-800'
};
