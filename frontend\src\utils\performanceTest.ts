// Performance testing utilities for chat application
// These utilities help test and validate performance improvements

import { ChatMessage } from '../contexts/ChatContext';
import { PERFORMANCE_CONFIG } from '../config/performance';

// Generate mock messages for performance testing
export const generateMockMessages = (count: number, averageLength: number = 500): ChatMessage[] => {
  const messages: ChatMessage[] = [];
  const messageTypes = ['user', 'answer', 'tool_use', 'tool_output', 'observation'];

  for (let i = 0; i < count; i++) {
    const isUser = i % 3 === 0; // Every 3rd message is from user
    const type = isUser ? 'user' : messageTypes[Math.floor(Math.random() * messageTypes.length)];
    const sender = isUser ? 'User' : 'Agent';

    // Generate content of varying lengths
    const contentLength = Math.floor(averageLength * (0.5 + Math.random()));
    const content = generateRandomText(contentLength);

    // Generate intermediate steps for some agent messages
    const intermediateSteps: ChatMessage[] = [];
    if (!isUser && Math.random() > 0.7) { // 30% chance of having intermediate steps
      const stepCount = Math.floor(Math.random() * 5) + 1;
      for (let j = 0; j < stepCount; j++) {
        intermediateSteps.push({
          id: `step-${i}-${j}`,
          thread_id: 1,
          content: generateRandomText(200),
          type: 'tool_use',
          from_sender: 'Agent',
          message_id: i * 1000 + j,
          agent_id: 'test-agent',
          user_id: 'test-user',
          created_at: new Date(Date.now() - (count - i) * 60000 + j * 1000).toISOString(),
        });
      }
    }

    messages.push({
      id: `msg-${i}`,
      thread_id: 1,
      content,
      type,
      from_sender: sender as 'User' | 'Agent',
      message_id: i,
      agent_id: isUser ? null : 'test-agent',
      user_id: 'test-user',
      created_at: new Date(Date.now() - (count - i) * 60000).toISOString(),
      intermediate_steps: intermediateSteps.length > 0 ? intermediateSteps : undefined,
    });
  }

  return messages;
};

// Generate random text for testing
const generateRandomText = (length: number): string => {
  const words = [
    'lorem', 'ipsum', 'dolor', 'sit', 'amet', 'consectetur', 'adipiscing', 'elit',
    'sed', 'do', 'eiusmod', 'tempor', 'incididunt', 'ut', 'labore', 'et', 'dolore',
    'magna', 'aliqua', 'enim', 'ad', 'minim', 'veniam', 'quis', 'nostrud',
    'exercitation', 'ullamco', 'laboris', 'nisi', 'aliquip', 'ex', 'ea', 'commodo',
    'consequat', 'duis', 'aute', 'irure', 'in', 'reprehenderit', 'voluptate',
    'velit', 'esse', 'cillum', 'fugiat', 'nulla', 'pariatur', 'excepteur', 'sint',
    'occaecat', 'cupidatat', 'non', 'proident', 'sunt', 'culpa', 'qui', 'officia',
    'deserunt', 'mollit', 'anim', 'id', 'est', 'laborum', 'code', 'function',
    'variable', 'component', 'react', 'typescript', 'javascript', 'performance',
    'optimization', 'memory', 'cache', 'virtual', 'scroll', 'pagination',
  ];

  const targetWords = Math.floor(length / 6); // Approximate words needed
  const result = [];

  for (let i = 0; i < targetWords; i++) {
    result.push(words[Math.floor(Math.random() * words.length)]);
  }

  return result.join(' ');
};

// Performance test scenarios
export const performanceTestScenarios = {
  // Small conversation test
  small: {
    messageCount: 50,
    averageMessageLength: 200,
    expectedPerformanceLevel: 'LOW',
    description: 'Small conversation with 50 messages',
  },

  // Medium conversation test
  medium: {
    messageCount: 200,
    averageMessageLength: 500,
    expectedPerformanceLevel: 'MEDIUM',
    description: 'Medium conversation with 200 messages',
  },

  // Large conversation test
  large: {
    messageCount: 500,
    averageMessageLength: 800,
    expectedPerformanceLevel: 'HIGH',
    description: 'Large conversation with 500 messages',
  },

  // Critical conversation test (the problematic scenario)
  critical: {
    messageCount: 1000,
    averageMessageLength: 1200,
    expectedPerformanceLevel: 'CRITICAL',
    description: 'Critical conversation with 1000+ messages (~90k tokens)',
  },

  // Extreme test
  extreme: {
    messageCount: 2000,
    averageMessageLength: 1500,
    expectedPerformanceLevel: 'CRITICAL',
    description: 'Extreme conversation for stress testing',
  },
};

// Performance measurement utilities
export const performanceMeasurement = {
  // Measure render time
  measureRenderTime: async (renderFunction: () => Promise<void> | void): Promise<number> => {
    const start = performance.now();
    await renderFunction();
    const end = performance.now();
    return end - start;
  },

  // Measure memory usage before and after an operation
  measureMemoryUsage: (operation: () => void): { before: number | null; after: number | null; delta: number | null } => {
    const getMemory = (): number | null => {
      if ('memory' in performance) {
        return (performance as any).memory.usedJSHeapSize;
      }
      return null;
    };

    const before = getMemory();
    operation();
    const after = getMemory();

    return {
      before,
      after,
      delta: before !== null && after !== null ? after - before : null,
    };
  },

  // Run a performance test scenario
  runScenario: async (scenarioName: keyof typeof performanceTestScenarios): Promise<{
    scenario: string;
    messages: ChatMessage[];
    renderTime: number;
    memoryUsage: { before: number | null; after: number | null; delta: number | null };
    tokenCount: number;
    passed: boolean;
  }> => {
    const scenario = performanceTestScenarios[scenarioName];
    console.log(`Running performance test: ${scenario.description}`);

    // Generate test messages
    const messages = generateMockMessages(scenario.messageCount, scenario.averageMessageLength);

    // Calculate token count
    const tokenCount = messages.reduce((total, msg) => {
      let msgTokens = (msg.content?.length || 0) / 4; // Rough token estimation
      if (msg.intermediate_steps) {
        msgTokens += msg.intermediate_steps.reduce((stepTotal, step) => 
          stepTotal + (step.content?.length || 0) / 4, 0);
      }
      return total + msgTokens;
    }, 0);

    // Measure render time and memory usage
    let renderTime = 0;
    const memoryUsage = performanceMeasurement.measureMemoryUsage(() => {
      // Simulate rendering (in a real test, this would render the actual components)
      const start = performance.now();
      // Simulate processing time
      for (let i = 0; i < messages.length; i++) {
        JSON.stringify(messages[i]); // Simulate processing
      }
      renderTime = performance.now() - start;
    });

    // Determine if test passed based on performance thresholds
    const passed = renderTime < PERFORMANCE_CONFIG.MONITORING.RENDER_TIME_THRESHOLD * 10; // Allow 10x threshold for large datasets

    return {
      scenario: scenario.description,
      messages,
      renderTime,
      memoryUsage,
      tokenCount: Math.round(tokenCount),
      passed,
    };
  },

  // Run all performance test scenarios
  runAllScenarios: async (): Promise<void> => {
    console.group('Performance Test Results');
    
    for (const scenarioName of Object.keys(performanceTestScenarios) as Array<keyof typeof performanceTestScenarios>) {
      const result = await performanceMeasurement.runScenario(scenarioName);
      
      console.group(`${result.scenario} - ${result.passed ? '✅ PASSED' : '❌ FAILED'}`);
      console.log(`Messages: ${result.messages.length}`);
      console.log(`Tokens: ${result.tokenCount.toLocaleString()}`);
      console.log(`Render Time: ${result.renderTime.toFixed(2)}ms`);
      console.log(`Memory Delta: ${result.memoryUsage.delta ? (result.memoryUsage.delta / 1024 / 1024).toFixed(2) + 'MB' : 'N/A'}`);
      console.groupEnd();
    }
    
    console.groupEnd();
  },
};

// Export for use in development/testing
export default {
  generateMockMessages,
  performanceTestScenarios,
  performanceMeasurement,
};
